# -*- coding: utf-8 -*-
"""
Sound Cog für ButterGolem

Enthält alle Audio-Commands für Voice-Channel-Funktionen.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
import os
import random
import asyncio
from typing import Optional, List

from .utils.decorators import error_handler, require_voice_channel, require_bot_permissions
from .utils.helpers import (
    create_embed, 
    create_error_embed, 
    create_success_embed,
    get_data_path
)
from .utils.logging_utils import log_command_usage

logger = logging.getLogger(__name__)


class SoundCog(commands.Cog, name="Sound"):
    """Audio-Commands für ButterGolem"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.voice_clients = {}  # Guild ID -> VoiceClient
        self.sound_queue = {}    # Guild ID -> Queue
        logger.info("Sound Cog geladen")
    
    def get_sound_files(self) -> List[str]:
        """Gibt eine Liste aller verfügbaren Sound-Dateien zurück"""
        try:
            clips_path = get_data_path('clips')
            if not os.path.exists(clips_path):
                return []
            
            sound_files = []
            for file in os.listdir(clips_path):
                if file.endswith(('.mp3', '.wav', '.ogg', '.m4a')):
                    # Entferne Dateiendung für Anzeige
                    sound_name = os.path.splitext(file)[0]
                    sound_files.append(sound_name)
            
            return sorted(sound_files)
        except Exception as e:
            logger.error(f"Fehler beim Laden der Sound-Dateien: {e}")
            return []
    
    def get_sound_file_path(self, sound_name: str) -> Optional[str]:
        """Gibt den vollständigen Pfad zu einer Sound-Datei zurück"""
        clips_path = get_data_path('clips')
        
        # Versuche verschiedene Dateiendungen
        extensions = ['.mp3', '.wav', '.ogg', '.m4a']
        
        for ext in extensions:
            file_path = os.path.join(clips_path, f"{sound_name}{ext}")
            if os.path.exists(file_path):
                return file_path
        
        return None
    
    @app_commands.command(
        name="play",
        description="Spielt eine Sound-Datei ab"
    )
    @app_commands.describe(
        sound="Name der Sound-Datei (ohne Dateiendung)"
    )
    @require_voice_channel
    @require_bot_permissions('connect', 'speak')
    @error_handler
    async def play_sound(self, interaction: discord.Interaction, sound: str):
        """Spielt eine Sound-Datei ab"""
        if not interaction.guild:
            await interaction.response.send_message(
                embed=create_error_embed("Dieser Command funktioniert nur auf Servern!"),
                ephemeral=True
            )
            return
        
        # Prüfe ob Sound-Datei existiert
        sound_path = self.get_sound_file_path(sound)
        if not sound_path:
            available_sounds = self.get_sound_files()
            if available_sounds:
                sounds_text = ", ".join(available_sounds[:10])
                if len(available_sounds) > 10:
                    sounds_text += f" (+{len(available_sounds) - 10} weitere)"
                error_msg = f"Sound '{sound}' nicht gefunden!\n\n**Verfügbare Sounds:** {sounds_text}"
            else:
                error_msg = "Keine Sound-Dateien gefunden!"
            
            await interaction.response.send_message(
                embed=create_error_embed(error_msg),
                ephemeral=True
            )
            return
        
        await interaction.response.defer()
        
        try:
            # Verbinde zu Voice Channel
            voice_channel = interaction.user.voice.channel
            
            # Prüfe ob Bot bereits verbunden ist
            voice_client = interaction.guild.voice_client
            if voice_client and voice_client.channel != voice_channel:
                await voice_client.move_to(voice_channel)
            elif not voice_client:
                voice_client = await voice_channel.connect()
            
            # Stoppe aktuellen Sound falls einer läuft
            if voice_client.is_playing():
                voice_client.stop()
            
            # Spiele Sound ab
            audio_source = discord.FFmpegPCMAudio(sound_path)
            voice_client.play(audio_source)
            
            embed = create_success_embed(
                f"🎵 Spiele **{sound}** in {voice_channel.mention}"
            )
            await interaction.followup.send(embed=embed)
            
            # Warte bis Sound fertig ist, dann disconnecte
            while voice_client.is_playing():
                await asyncio.sleep(1)
            
            await voice_client.disconnect()
            
            await log_command_usage(self.bot, interaction, "play", True)
            
        except Exception as e:
            logger.error(f"Fehler beim Abspielen von {sound}: {e}")
            await interaction.followup.send(
                embed=create_error_embed(f"Fehler beim Abspielen: {str(e)}"),
                ephemeral=True
            )
    
    @app_commands.command(
        name="random",
        description="Spielt einen zufälligen Sound ab"
    )
    @require_voice_channel
    @require_bot_permissions('connect', 'speak')
    @error_handler
    async def random_sound(self, interaction: discord.Interaction):
        """Spielt einen zufälligen Sound ab"""
        available_sounds = self.get_sound_files()
        
        if not available_sounds:
            await interaction.response.send_message(
                embed=create_error_embed("Keine Sound-Dateien gefunden!"),
                ephemeral=True
            )
            return
        
        random_sound = random.choice(available_sounds)
        
        # Rufe play_sound mit dem zufälligen Sound auf
        await self.play_sound(interaction, random_sound)
    
    @app_commands.command(
        name="sounds",
        description="Zeigt alle verfügbaren Sounds an"
    )
    @error_handler
    async def list_sounds(self, interaction: discord.Interaction):
        """Zeigt alle verfügbaren Sound-Dateien an"""
        available_sounds = self.get_sound_files()
        
        if not available_sounds:
            await interaction.response.send_message(
                embed=create_error_embed("Keine Sound-Dateien gefunden!"),
                ephemeral=True
            )
            return
        
        # Teile Sounds in Chunks für bessere Darstellung
        chunk_size = 20
        sound_chunks = [available_sounds[i:i + chunk_size] for i in range(0, len(available_sounds), chunk_size)]
        
        embeds = []
        for i, chunk in enumerate(sound_chunks):
            sounds_text = "\n".join([f"• `{sound}`" for sound in chunk])
            
            embed = create_embed(
                title=f"🎵 Verfügbare Sounds ({len(available_sounds)} total)",
                description=sounds_text,
                color=discord.Color.blue(),
                footer=f"Seite {i + 1}/{len(sound_chunks)} • Verwende /play <soundname> zum Abspielen"
            )
            embeds.append(embed)
        
        # Sende erste Seite
        await interaction.response.send_message(embed=embeds[0])
        
        # Sende weitere Seiten falls vorhanden
        if len(embeds) > 1:
            for embed in embeds[1:]:
                await interaction.followup.send(embed=embed, ephemeral=True)
        
        await log_command_usage(self.bot, interaction, "sounds", True)
    
    @app_commands.command(
        name="stop",
        description="Stoppt die Audiowiedergabe und disconnectet den Bot"
    )
    @error_handler
    async def stop_audio(self, interaction: discord.Interaction):
        """Stoppt Audio und disconnectet Bot"""
        if not interaction.guild:
            await interaction.response.send_message(
                embed=create_error_embed("Dieser Command funktioniert nur auf Servern!"),
                ephemeral=True
            )
            return
        
        voice_client = interaction.guild.voice_client
        
        if not voice_client:
            await interaction.response.send_message(
                embed=create_error_embed("Bot ist nicht in einem Voice Channel!"),
                ephemeral=True
            )
            return
        
        try:
            if voice_client.is_playing():
                voice_client.stop()
            
            await voice_client.disconnect()
            
            embed = create_success_embed(
                "🔇 Audio gestoppt und Voice Channel verlassen"
            )
            await interaction.response.send_message(embed=embed)
            
            await log_command_usage(self.bot, interaction, "stop", True)
            
        except Exception as e:
            logger.error(f"Fehler beim Stoppen: {e}")
            await interaction.response.send_message(
                embed=create_error_embed(f"Fehler beim Stoppen: {str(e)}"),
                ephemeral=True
            )
    
    @app_commands.command(
        name="volume",
        description="Ändert die Lautstärke (nur für zukünftige Sounds)"
    )
    @app_commands.describe(
        level="Lautstärke zwischen 0 und 100"
    )
    @error_handler
    async def set_volume(self, interaction: discord.Interaction, level: int):
        """Setzt die Lautstärke"""
        if level < 0 or level > 100:
            await interaction.response.send_message(
                embed=create_error_embed("Lautstärke muss zwischen 0 und 100 liegen!"),
                ephemeral=True
            )
            return
        
        # Speichere Lautstärke für Guild (vereinfacht)
        if not hasattr(self, 'guild_volumes'):
            self.guild_volumes = {}
        
        self.guild_volumes[interaction.guild.id] = level / 100.0
        
        embed = create_success_embed(
            f"🔊 Lautstärke auf {level}% gesetzt\n(Gilt für zukünftige Sounds)"
        )
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "volume", True)
    
    @app_commands.command(
        name="join",
        description="Lässt den Bot deinem Voice Channel beitreten"
    )
    @require_voice_channel
    @require_bot_permissions('connect')
    @error_handler
    async def join_voice(self, interaction: discord.Interaction):
        """Bot tritt Voice Channel bei"""
        voice_channel = interaction.user.voice.channel
        
        try:
            voice_client = interaction.guild.voice_client
            
            if voice_client:
                if voice_client.channel == voice_channel:
                    await interaction.response.send_message(
                        embed=create_error_embed("Bot ist bereits in diesem Voice Channel!"),
                        ephemeral=True
                    )
                    return
                else:
                    await voice_client.move_to(voice_channel)
                    action = "gewechselt zu"
            else:
                await voice_channel.connect()
                action = "beigetreten"
            
            embed = create_success_embed(
                f"🔊 Bot ist {action} {voice_channel.mention}"
            )
            await interaction.response.send_message(embed=embed)
            
            await log_command_usage(self.bot, interaction, "join", True)
            
        except Exception as e:
            logger.error(f"Fehler beim Beitreten: {e}")
            await interaction.response.send_message(
                embed=create_error_embed(f"Fehler beim Beitreten: {str(e)}"),
                ephemeral=True
            )
    
    @app_commands.command(
        name="leave",
        description="Lässt den Bot den Voice Channel verlassen"
    )
    @error_handler
    async def leave_voice(self, interaction: discord.Interaction):
        """Bot verlässt Voice Channel"""
        if not interaction.guild:
            await interaction.response.send_message(
                embed=create_error_embed("Dieser Command funktioniert nur auf Servern!"),
                ephemeral=True
            )
            return
        
        voice_client = interaction.guild.voice_client
        
        if not voice_client:
            await interaction.response.send_message(
                embed=create_error_embed("Bot ist nicht in einem Voice Channel!"),
                ephemeral=True
            )
            return
        
        try:
            channel_name = voice_client.channel.name
            await voice_client.disconnect()
            
            embed = create_success_embed(
                f"👋 Bot hat {channel_name} verlassen"
            )
            await interaction.response.send_message(embed=embed)
            
            await log_command_usage(self.bot, interaction, "leave", True)
            
        except Exception as e:
            logger.error(f"Fehler beim Verlassen: {e}")
            await interaction.response.send_message(
                embed=create_error_embed(f"Fehler beim Verlassen: {str(e)}"),
                ephemeral=True
            )
    
    # Autocomplete für play command
    @play_sound.autocomplete('sound')
    async def sound_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        """Autocomplete für Sound-Namen"""
        available_sounds = self.get_sound_files()
        
        # Filtere Sounds basierend auf Eingabe
        if current:
            filtered_sounds = [sound for sound in available_sounds if current.lower() in sound.lower()]
        else:
            filtered_sounds = available_sounds
        
        # Limitiere auf 25 Ergebnisse (Discord-Limit)
        return [
            app_commands.Choice(name=sound, value=sound)
            for sound in filtered_sounds[:25]
        ]


async def setup(bot: commands.Bot):
    """Setup-Funktion für das Cog"""
    await bot.add_cog(SoundCog(bot))