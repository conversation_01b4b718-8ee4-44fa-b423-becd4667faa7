# -*- coding: utf-8 -*-
"""
Info Cog für ButterGolem

Enthält alle Informations-Commands wie ping, info, help etc.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
import time
import psutil
from datetime import datetime, timedelta
from typing import Optional

from .utils.decorators import error_handler
from .utils.helpers import (
    create_embed, 
    create_error_embed,
    format_uptime
)
from .utils.logging_utils import log_command_usage

logger = logging.getLogger(__name__)


class InfoCog(commands.Cog, name="Info"):
    """Informations-Commands für ButterGolem"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.start_time = time.time()
        logger.info("Info Cog geladen")
    
    @app_commands.command(
        name="ping",
        description="Zeigt die Bot-Latenz und weitere Informationen"
    )
    @error_handler
    async def ping_command(self, interaction: discord.Interaction):
        """<PERSON><PERSON><PERSON>-Latenz und Informationen"""
        # Messe Response-Zeit
        start_time = time.time()
        
        # Erstelle initiales Embed
        embed = create_embed(
            title="🏓 Pong!",
            description="Messe Latenz...",
            color=discord.Color.blue()
        )
        
        await interaction.response.send_message(embed=embed)
        
        # Berechne Response-Zeit
        end_time = time.time()
        response_time = round((end_time - start_time) * 1000)
        
        # Bot-Statistiken
        guild_count = len(self.bot.guilds)
        user_count = sum(guild.member_count for guild in self.bot.guilds if guild.member_count)
        
        # System-Informationen
        try:
            process = psutil.Process()
            memory_usage = process.memory_info().rss / 1024 / 1024  # MB
            cpu_usage = process.cpu_percent()
        except:
            memory_usage = 0
            cpu_usage = 0
        
        # Uptime
        uptime_seconds = int(time.time() - self.start_time)
        uptime_str = format_uptime(uptime_seconds)
        
        # Aktualisiere Embed
        updated_embed = create_embed(
            title="🏓 Pong!",
            color=discord.Color.green(),
            fields=[
                {"name": "📡 Latenz", "value": f"WebSocket: {round(self.bot.latency * 1000)}ms\nResponse: {response_time}ms", "inline": True},
                {"name": "📊 Statistiken", "value": f"Server: {guild_count}\nUser: {user_count:,}", "inline": True},
                {"name": "💾 System", "value": f"RAM: {memory_usage:.1f} MB\nCPU: {cpu_usage}%", "inline": True},
                {"name": "⏱️ Uptime", "value": uptime_str, "inline": True},
                {"name": "🔧 Cogs", "value": f"Geladen: {len(self.bot.cogs)}", "inline": True},
                {"name": "🐍 Version", "value": f"Discord.py {discord.__version__}", "inline": True}
            ]
        )
        
        await interaction.edit_original_response(embed=updated_embed)
        await log_command_usage(self.bot, interaction, "ping", True)
    
    @app_commands.command(
        name="info",
        description="Zeigt Informationen über den Bot"
    )
    @error_handler
    async def info_command(self, interaction: discord.Interaction):
        """Zeigt Bot-Informationen"""
        # Bot-Informationen
        bot_user = self.bot.user
        created_at = bot_user.created_at.strftime("%d.%m.%Y")
        
        # Statistiken
        guild_count = len(self.bot.guilds)
        user_count = sum(guild.member_count for guild in self.bot.guilds if guild.member_count)
        
        # Uptime
        uptime_seconds = int(time.time() - self.start_time)
        uptime_str = format_uptime(uptime_seconds)
        
        embed = create_embed(
            title="🤖 ButterGolem Bot Info",
            description="Ein Discord-Bot mit vielen nützlichen Features!",
            color=discord.Color.gold(),
            thumbnail=bot_user.display_avatar.url,
            fields=[
                {"name": "👤 Bot", "value": f"Name: {bot_user.name}\nID: {bot_user.id}\nErstellt: {created_at}", "inline": True},
                {"name": "📊 Statistiken", "value": f"Server: {guild_count}\nUser: {user_count:,}\nCogs: {len(self.bot.cogs)}", "inline": True},
                {"name": "⏱️ Status", "value": f"Uptime: {uptime_str}\nLatenz: {round(self.bot.latency * 1000)}ms", "inline": True},
                {"name": "🔧 Features", "value": "• Admin-Commands\n• Spaß-Commands\n• Sound-Commands\n• Quiz-System\n• ButterIQ AI", "inline": False},
                {"name": "🔗 Links", "value": "[GitHub](https://github.com/your-repo) • [Support](https://discord.gg/your-invite)", "inline": False}
            ],
            footer="ButterGolem - Dein Discord-Begleiter"
        )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "info", True)
    
    @app_commands.command(
        name="help",
        description="Zeigt alle verfügbaren Commands"
    )
    @app_commands.describe(
        kategorie="Spezifische Kategorie anzeigen (optional)"
    )
    @error_handler
    async def help_command(self, interaction: discord.Interaction, kategorie: Optional[str] = None):
        """Zeigt Hilfe für Commands"""
        if kategorie:
            # Zeige spezifische Kategorie
            cog = self.bot.get_cog(kategorie.title())
            if not cog:
                await interaction.response.send_message(
                    embed=create_error_embed(f"Kategorie '{kategorie}' nicht gefunden!"),
                    ephemeral=True
                )
                return
            
            commands_list = [cmd for cmd in cog.get_app_commands()]
            if not commands_list:
                await interaction.response.send_message(
                    embed=create_error_embed(f"Keine Commands in Kategorie '{kategorie}' gefunden!"),
                    ephemeral=True
                )
                return
            
            command_text = "\n".join([f"• `/{cmd.name}` - {cmd.description}" for cmd in commands_list])
            
            embed = create_embed(
                title=f"📚 {kategorie.title()} Commands",
                description=command_text,
                color=discord.Color.blue()
            )
            
        else:
            # Zeige alle Kategorien
            cog_info = []
            
            for cog_name, cog in self.bot.cogs.items():
                commands_count = len([cmd for cmd in cog.get_app_commands()])
                if commands_count > 0:
                    cog_info.append(f"**{cog_name}** - {commands_count} Commands")
            
            embed = create_embed(
                title="📚 ButterGolem Hilfe",
                description="Verfügbare Command-Kategorien:\n\n" + "\n".join(cog_info),
                color=discord.Color.blue(),
                fields=[
                    {"name": "💡 Tipp", "value": "Verwende `/help <kategorie>` für detaillierte Informationen zu einer Kategorie.", "inline": False},
                    {"name": "🔧 Beispiele", "value": "`/help fun` - Zeigt alle Spaß-Commands\n`/help admin` - Zeigt alle Admin-Commands", "inline": False}
                ]
            )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "help", True)
    
    @app_commands.command(
        name="server",
        description="Zeigt Informationen über den aktuellen Server"
    )
    @error_handler
    async def server_command(self, interaction: discord.Interaction):
        """Zeigt Server-Informationen"""
        if not interaction.guild:
            await interaction.response.send_message(
                embed=create_error_embed("Dieser Command funktioniert nur auf Servern!"),
                ephemeral=True
            )
            return
        
        guild = interaction.guild
        
        # Server-Statistiken
        member_count = guild.member_count
        channel_count = len(guild.channels)
        role_count = len(guild.roles)
        emoji_count = len(guild.emojis)
        
        # Server-Features
        features = []
        if guild.premium_tier > 0:
            features.append(f"Nitro Level {guild.premium_tier}")
        if guild.verification_level != discord.VerificationLevel.none:
            features.append(f"Verifizierung: {guild.verification_level.name}")
        
        features_text = "\n".join(features) if features else "Keine besonderen Features"
        
        # Erstellt-Datum
        created_at = guild.created_at.strftime("%d.%m.%Y um %H:%M")
        
        embed = create_embed(
            title=f"🏠 {guild.name}",
            color=discord.Color.blue(),
            thumbnail=guild.icon.url if guild.icon else None,
            fields=[
                {"name": "📊 Statistiken", "value": f"Mitglieder: {member_count:,}\nChannels: {channel_count}\nRollen: {role_count}\nEmojis: {emoji_count}", "inline": True},
                {"name": "👑 Owner", "value": guild.owner.mention if guild.owner else "Unbekannt", "inline": True},
                {"name": "📅 Erstellt", "value": created_at, "inline": True},
                {"name": "🔧 Features", "value": features_text, "inline": False}
            ],
            footer=f"Server-ID: {guild.id}"
        )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "server", True)
    
    @app_commands.command(
        name="user",
        description="Zeigt Informationen über einen User"
    )
    @app_commands.describe(
        user="Der User über den Informationen angezeigt werden sollen (optional)"
    )
    @error_handler
    async def user_command(self, interaction: discord.Interaction, user: Optional[discord.Member] = None):
        """Zeigt User-Informationen"""
        target_user = user or interaction.user
        
        # User-Informationen
        created_at = target_user.created_at.strftime("%d.%m.%Y um %H:%M")
        
        # Server-spezifische Informationen (falls auf Server)
        if interaction.guild and isinstance(target_user, discord.Member):
            joined_at = target_user.joined_at.strftime("%d.%m.%Y um %H:%M") if target_user.joined_at else "Unbekannt"
            roles = [role.mention for role in target_user.roles[1:]]  # Ohne @everyone
            roles_text = ", ".join(roles[:10]) if roles else "Keine Rollen"
            if len(roles) > 10:
                roles_text += f" (+{len(roles) - 10} weitere)"
        else:
            joined_at = "Nicht auf diesem Server"
            roles_text = "Nicht verfügbar"
        
        # Status
        status_emoji = {
            discord.Status.online: "🟢",
            discord.Status.idle: "🟡",
            discord.Status.dnd: "🔴",
            discord.Status.offline: "⚫"
        }
        
        status = status_emoji.get(target_user.status, "❓") + " " + target_user.status.name.title()
        
        embed = create_embed(
            title=f"👤 {target_user.display_name}",
            color=target_user.color if hasattr(target_user, 'color') and target_user.color != discord.Color.default() else discord.Color.blue(),
            thumbnail=target_user.display_avatar.url,
            fields=[
                {"name": "📝 Info", "value": f"Username: {target_user.name}\nID: {target_user.id}\nStatus: {status}", "inline": True},
                {"name": "📅 Daten", "value": f"Erstellt: {created_at}\nBeigetreten: {joined_at}", "inline": True},
                {"name": "🎭 Rollen", "value": roles_text, "inline": False}
            ]
        )
        
        # Bot-Badge hinzufügen
        if target_user.bot:
            embed.add_field(name="🤖 Bot", value="Dieser User ist ein Bot", inline=True)
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "user", True)
    
    @app_commands.command(
        name="uptime",
        description="Zeigt die Bot-Uptime"
    )
    @error_handler
    async def uptime_command(self, interaction: discord.Interaction):
        """Zeigt Bot-Uptime"""
        uptime_seconds = int(time.time() - self.start_time)
        uptime_str = format_uptime(uptime_seconds)
        
        embed = create_embed(
            title="⏱️ Bot Uptime",
            description=f"Der Bot läuft seit: **{uptime_str}**",
            color=discord.Color.green(),
            fields=[
                {"name": "📊 Details", "value": f"Gestartet: <t:{int(self.start_time)}:F>\nSekunden: {uptime_seconds:,}", "inline": False}
            ]
        )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "uptime", True)


async def setup(bot: commands.Bot):
    """Setup-Funktion für das Cog"""
    await bot.add_cog(InfoCog(bot))