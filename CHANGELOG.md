# Changelog

## Version 6.2.0 - Performance & Stability Update (06. Juni 2025)

### Neue Features
- Aktualisierung der Bot-Version auf 6.2.0 in allen Systemdateien
- Verbessertes Changelog-System mit sauberer Versionsverfolgung
- Überarbeitete README-Dokumentation mit shields.io Badges
- Korrektur der Lizenz auf GNU v3 statt MIT
- Entfernung von Ko-fi Links, Ersetzung durch Monero Spendeninformationen
- Vollständige und korrekte Slash Command Dokumentation
- Vereinfachte Codebasis mit reduzierter Redundanz
- Konsistente Versionsreferenzen im gesamten Bot

### Bug Fixes
- Behobene Versionsinkonsistenzen über mehrere Dateien hinweg
- Bereinigte Changelog-Einträge zur Vermeidung von Duplikaten
- Standardisiertes Versionsanzeigeformat im gesamten Bot
- Korrektur der Slash Command Dokumentation in README.md
- Entfernung falscher Spendenlinks (Ko-fi)
- Aktualisierung der Lizenzinformation auf korrekte GNU v3
- Aktualisiertes Hilfesystem mit korrekten Versionsinformationen

### Technische Änderungen
- Aktualisierte main.py Bot-Beschreibung auf 6.2.0
- Aktualisierte slash_commands.py Versionsanzeige
- Aktualisierte hilfe.py Versionsreferenzen
- Aktualisierte animated_stats.py Versionszeichenketten
- Hinzugefügter 6.2.0 Eintrag im Changelog-System
- Veraltete Ko-fi Integration und Webhook-Handler entfernt

## Version 6.1.0 - Admin Command Visibility & Changelog Fix (04. Juli 2025)

### Neue Features
- Admin Commands sind jetzt für normale User unsichtbar
- Implementierung von @app_commands.default_permissions(administrator=True)
- Verbesserte User Experience - keine Verwirrung mehr durch sichtbare aber nicht ausführbare Commands
- Native Discord Permission System Integration
- Changelog System wiederhergestellt mit /changelog command
- Entfernung des redundanten /lordupdate Commands
- Vollständige Versionhistorie verfügbar
- Detaillierte Changelog-Ansicht für spezifische Versionen

### Bug Fixes
- Admin Commands werden nicht mehr in der Slash Command Liste für normale User angezeigt
- Changelog Commands sind wieder funktional
- Command Registration Issues behoben
- Verbesserte Permission Handling für alle Admin-Funktionen

### Technische Änderungen
- @app_commands.default_permissions(administrator=True) zu allen Admin Commands hinzugefügt
- register_update_commands in main.py wieder aktiviert
- ChangelogCog Registration in main.py hinzugefügt
- Redundanten /lordupdate Command aus updates.py entfernt
- Version Strings in slash_commands.py und main.py auf 6.1.0 aktualisiert

## Version 6.0.0 - Das große Update (2025)

### Neue Features
- Komplette Slash Command Migration - Alle ! Prefix Commands entfernt
- 15+ neue Slash Commands implementiert
- Moderne Discord-Integration mit Auto-Complete
- Ephemeral Responses für bessere UX
- Intent-freie Architektur - Keine privilegierten Intents mehr benötigt
- Optimiert für 100+ Server
- Massiv erweiterte Drachenlord Lore (2024/2025)
- Intelligente Chat-Antworten
- Kontextbewusste Konversationen
- Authentische Persönlichkeit
- 500+ Soundclips organisiert und optimiert
- Intelligentes Sound-Caching
- Verbesserte Playback-Qualität
- /sound Command mit Auto-Complete
- Memory-System für persistente Daten
- Neofetch-Style animierte Statistiken
- Erweiterte Debug-Funktionen
- Global Messaging System
- Ban-Management für User und Server
- Ko-fi Integration für Spenden
- Verbesserte Hilfe-Systeme
- Privacy Policy Integration
- Kontakt-Informationen

### Bug Fixes
- Voice Channel Verbindungsprobleme behoben
- Memory Leaks in Sound-System gefixt
- Rate Limiting verbessert
- Error Handling für alle Commands
- Embed-Formatierung korrigiert
- Unicode-Probleme in Texten behoben

### Technische Verbesserungen
- Modulare Struktur mit sauberer Trennung
- Admin-Module in eigenem Ordner
- KI-Daten strukturiert in JSON-Dateien
- Verbesserte Error-Behandlung
- Entfernung von Message Content Intent
- Effiziente Embed-Generierung
- Optimierte Datenbank-Zugriffe
- Reduzierte API-Calls
- Docker-Container optimiert
- Environment Variables für Konfiguration
- Verbesserte Logging-Systeme
- Automatische Command-Synchronisation

### Breaking Changes
- Alle !drache Commands entfernt
- Neue Syntax: /drache stats statt !drache stats
- Alle Commands jetzt als Slash Commands verfügbar
- Message Content Intent nicht mehr benötigt
- Reduzierte Berechtigungsanforderungen
- Bessere Sicherheit durch weniger Intents

### Neue Slash Commands

#### Nutzer Commands
- /drache stats - Erweiterte Bot-Statistiken
- /drache neofetch - Animierte System-Informationen
- /sound [name] - Spezifischen Sound abspielen
- /sounds - Alle verfügbaren Sounds anzeigen
- /lord - Zufälligen Sound abspielen
- /zitat - Drachenlord Zitat
- /mett - Mett-Meme
- /lordmeme - Zufälliges Meme
- /quiz - Quiz starten
- /ping - Bot-Latenz prüfen
- /hilfe - Hilfe-System
- /kontakt - Kontakt-Informationen
- /privacy - Datenschutz-Informationen

#### Admin Commands
- /memory [add/remove/list] - Memory-System verwalten
- /servercount - Server-Anzahl anzeigen
- /server [info/list] - Server-Informationen
- /antwort [message] - Global Message senden
- /debug_sounds - Sound-System debuggen
- /butteriq [user] - User-Statistiken
- /global [message] - Globale Nachricht
