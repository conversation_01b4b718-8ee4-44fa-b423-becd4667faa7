# -*- coding: utf-8 -*-
"""
Quiz Cog für ButterGolem

Enthält Quiz-System mit verschiedenen Kategorien und Schwierigkeitsgraden.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
import random
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

from .utils.decorators import error_handler
from .utils.helpers import (
    create_embed, 
    create_error_embed, 
    create_success_embed,
    load_json_data,
    save_json_data
)
from .utils.logging_utils import log_command_usage

logger = logging.getLogger(__name__)


class QuizSession:
    """Repräsentiert eine aktive Quiz-Session"""
    
    def __init__(self, channel_id: int, user_id: int, questions: List[Dict], category: str):
        self.channel_id = channel_id
        self.user_id = user_id
        self.questions = questions
        self.category = category
        self.current_question = 0
        self.score = 0
        self.start_time = datetime.now()
        self.answers = []
    
    def get_current_question(self) -> Optional[Dict]:
        """Gibt die aktuelle Frage zurück"""
        if self.current_question < len(self.questions):
            return self.questions[self.current_question]
        return None
    
    def answer_question(self, answer: str) -> bool:
        """Beantwortet die aktuelle Frage und gibt zurück ob richtig"""
        question = self.get_current_question()
        if not question:
            return False
        
        correct = answer.lower() == question['correct_answer'].lower()
        
        self.answers.append({
            'question': question['question'],
            'user_answer': answer,
            'correct_answer': question['correct_answer'],
            'correct': correct
        })
        
        if correct:
            self.score += 1
        
        self.current_question += 1
        return correct
    
    def is_finished(self) -> bool:
        """Prüft ob Quiz beendet ist"""
        return self.current_question >= len(self.questions)
    
    def get_duration(self) -> timedelta:
        """Gibt die Dauer des Quiz zurück"""
        return datetime.now() - self.start_time


class QuizCog(commands.Cog, name="Quiz"):
    """Quiz-System für ButterGolem"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.active_sessions = {}  # channel_id -> QuizSession
        self.quiz_data = self.load_quiz_data()
        self.user_stats = self.load_user_stats()
        logger.info("Quiz Cog geladen")
    
    def load_quiz_data(self) -> Dict[str, List[Dict]]:
        """Lädt Quiz-Daten aus JSON-Dateien"""
        try:
            quiz_data = load_json_data('quiz_questions.json')
            if quiz_data:
                return quiz_data
            
            # Fallback: Erstelle Beispiel-Quiz-Daten
            default_quiz = {
                "allgemein": [
                    {
                        "question": "Wie viele Kontinente gibt es?",
                        "options": ["5", "6", "7", "8"],
                        "correct_answer": "7",
                        "difficulty": "easy"
                    },
                    {
                        "question": "Welches ist das größte Säugetier der Welt?",
                        "options": ["Elefant", "Blauwal", "Giraffe", "Nilpferd"],
                        "correct_answer": "Blauwal",
                        "difficulty": "easy"
                    }
                ],
                "gaming": [
                    {
                        "question": "In welchem Jahr wurde Minecraft veröffentlicht?",
                        "options": ["2009", "2010", "2011", "2012"],
                        "correct_answer": "2011",
                        "difficulty": "medium"
                    }
                ],
                "wissenschaft": [
                    {
                        "question": "Was ist die chemische Formel für Wasser?",
                        "options": ["H2O", "CO2", "NaCl", "O2"],
                        "correct_answer": "H2O",
                        "difficulty": "easy"
                    }
                ]
            }
            
            # Speichere Default-Daten
            save_json_data('quiz_questions.json', default_quiz)
            return default_quiz
            
        except Exception as e:
            logger.error(f"Fehler beim Laden der Quiz-Daten: {e}")
            return {}
    
    def load_user_stats(self) -> Dict[str, Dict]:
        """Lädt User-Statistiken"""
        try:
            stats = load_json_data('quiz_stats.json')
            return stats if stats else {}
        except Exception as e:
            logger.error(f"Fehler beim Laden der Quiz-Statistiken: {e}")
            return {}
    
    def save_user_stats(self) -> None:
        """Speichert User-Statistiken"""
        try:
            save_json_data('quiz_stats.json', self.user_stats)
        except Exception as e:
            logger.error(f"Fehler beim Speichern der Quiz-Statistiken: {e}")
    
    def update_user_stats(self, user_id: int, session: QuizSession) -> None:
        """Aktualisiert User-Statistiken nach Quiz"""
        user_id_str = str(user_id)
        
        if user_id_str not in self.user_stats:
            self.user_stats[user_id_str] = {
                'total_quizzes': 0,
                'total_questions': 0,
                'correct_answers': 0,
                'categories': {},
                'best_streak': 0,
                'total_time': 0
            }
        
        stats = self.user_stats[user_id_str]
        stats['total_quizzes'] += 1
        stats['total_questions'] += len(session.questions)
        stats['correct_answers'] += session.score
        stats['total_time'] += session.get_duration().total_seconds()
        
        # Kategorie-spezifische Stats
        if session.category not in stats['categories']:
            stats['categories'][session.category] = {
                'quizzes': 0,
                'questions': 0,
                'correct': 0
            }
        
        cat_stats = stats['categories'][session.category]
        cat_stats['quizzes'] += 1
        cat_stats['questions'] += len(session.questions)
        cat_stats['correct'] += session.score
        
        self.save_user_stats()
    
    @app_commands.command(
        name="quiz",
        description="Startet ein Quiz"
    )
    @app_commands.describe(
        kategorie="Quiz-Kategorie (optional)",
        anzahl="Anzahl der Fragen (1-20, Standard: 5)"
    )
    @error_handler
    async def start_quiz(self, interaction: discord.Interaction, kategorie: Optional[str] = None, anzahl: Optional[int] = 5):
        """Startet ein neues Quiz"""
        # Prüfe ob bereits ein Quiz läuft
        if interaction.channel.id in self.active_sessions:
            await interaction.response.send_message(
                embed=create_error_embed("In diesem Channel läuft bereits ein Quiz!"),
                ephemeral=True
            )
            return
        
        # Validiere Anzahl
        if anzahl < 1 or anzahl > 20:
            await interaction.response.send_message(
                embed=create_error_embed("Anzahl muss zwischen 1 und 20 liegen!"),
                ephemeral=True
            )
            return
        
        # Wähle Kategorie
        if kategorie:
            if kategorie.lower() not in self.quiz_data:
                available_categories = ", ".join(self.quiz_data.keys())
                await interaction.response.send_message(
                    embed=create_error_embed(
                        f"Kategorie '{kategorie}' nicht gefunden!\n\n"
                        f"**Verfügbare Kategorien:** {available_categories}"
                    ),
                    ephemeral=True
                )
                return
            selected_category = kategorie.lower()
        else:
            # Zufällige Kategorie
            selected_category = random.choice(list(self.quiz_data.keys()))
        
        # Wähle Fragen
        available_questions = self.quiz_data[selected_category]
        if len(available_questions) < anzahl:
            await interaction.response.send_message(
                embed=create_error_embed(
                    f"Nicht genügend Fragen in Kategorie '{selected_category}'!\n"
                    f"Verfügbar: {len(available_questions)}, Angefragt: {anzahl}"
                ),
                ephemeral=True
            )
            return
        
        selected_questions = random.sample(available_questions, anzahl)
        
        # Erstelle Quiz-Session
        session = QuizSession(
            interaction.channel.id,
            interaction.user.id,
            selected_questions,
            selected_category
        )
        
        self.active_sessions[interaction.channel.id] = session
        
        # Sende erste Frage
        await self.send_question(interaction, session)
        await log_command_usage(self.bot, interaction, "quiz", True)
    
    async def send_question(self, interaction: discord.Interaction, session: QuizSession, edit: bool = False):
        """Sendet die aktuelle Frage"""
        question_data = session.get_current_question()
        if not question_data:
            await self.end_quiz(interaction, session)
            return
        
        # Erstelle Embed
        embed = create_embed(
            title=f"📝 Quiz - {session.category.title()}",
            description=f"**Frage {session.current_question + 1}/{len(session.questions)}**\n\n{question_data['question']}",
            color=discord.Color.blue(),
            fields=[
                {"name": "Optionen", "value": "\n".join([f"{chr(65+i)}. {option}" for i, option in enumerate(question_data['options'])]), "inline": False},
                {"name": "Score", "value": f"{session.score}/{session.current_question}", "inline": True},
                {"name": "Zeit", "value": "30 Sekunden", "inline": True}
            ],
            footer="Antworte mit A, B, C oder D"
        )
        
        if edit:
            await interaction.edit_original_response(embed=embed)
        else:
            await interaction.response.send_message(embed=embed)
        
        # Warte auf Antwort
        def check(message):
            return (
                message.author.id == session.user_id and
                message.channel.id == session.channel_id and
                message.content.upper() in ['A', 'B', 'C', 'D']
            )
        
        try:
            response = await self.bot.wait_for('message', check=check, timeout=30.0)
            
            # Verarbeite Antwort
            answer_index = ord(response.content.upper()) - ord('A')
            if 0 <= answer_index < len(question_data['options']):
                user_answer = question_data['options'][answer_index]
                correct = session.answer_question(user_answer)
                
                # Zeige Ergebnis
                result_embed = create_embed(
                    title="✅ Richtig!" if correct else "❌ Falsch!",
                    description=f"**Richtige Antwort:** {question_data['correct_answer']}",
                    color=discord.Color.green() if correct else discord.Color.red()
                )
                
                await response.reply(embed=result_embed, delete_after=3)
                
                # Lösche User-Antwort
                try:
                    await response.delete()
                except:
                    pass
                
                # Warte kurz, dann nächste Frage
                await asyncio.sleep(2)
                
                if session.is_finished():
                    await self.end_quiz(interaction, session)
                else:
                    await self.send_question(interaction, session, edit=True)
            
        except asyncio.TimeoutError:
            # Zeit abgelaufen
            session.answer_question("")
            
            timeout_embed = create_embed(
                title="⏰ Zeit abgelaufen!",
                description=f"**Richtige Antwort:** {question_data['correct_answer']}",
                color=discord.Color.orange()
            )
            
            await interaction.followup.send(embed=timeout_embed, ephemeral=True)
            
            await asyncio.sleep(2)
            
            if session.is_finished():
                await self.end_quiz(interaction, session)
            else:
                await self.send_question(interaction, session, edit=True)
    
    async def end_quiz(self, interaction: discord.Interaction, session: QuizSession):
        """Beendet das Quiz und zeigt Ergebnisse"""
        # Entferne Session
        if session.channel_id in self.active_sessions:
            del self.active_sessions[session.channel_id]
        
        # Aktualisiere Statistiken
        self.update_user_stats(session.user_id, session)
        
        # Berechne Prozentsatz
        percentage = (session.score / len(session.questions)) * 100
        duration = session.get_duration()
        
        # Bestimme Bewertung
        if percentage >= 90:
            rating = "🏆 Ausgezeichnet!"
            color = discord.Color.gold()
        elif percentage >= 70:
            rating = "🥈 Gut gemacht!"
            color = discord.Color.green()
        elif percentage >= 50:
            rating = "🥉 Nicht schlecht!"
            color = discord.Color.orange()
        else:
            rating = "📚 Übung macht den Meister!"
            color = discord.Color.red()
        
        # Erstelle Ergebnis-Embed
        embed = create_embed(
            title="🎯 Quiz beendet!",
            description=f"{rating}\n\n**Ergebnis:** {session.score}/{len(session.questions)} ({percentage:.1f}%)",
            color=color,
            fields=[
                {"name": "📊 Details", "value": f"Kategorie: {session.category.title()}\nDauer: {duration.total_seconds():.1f}s", "inline": True},
                {"name": "🎮 Commands", "value": "`/quiz` - Neues Quiz\n`/quiz_stats` - Deine Statistiken", "inline": True}
            ]
        )
        
        await interaction.edit_original_response(embed=embed)
    
    @app_commands.command(
        name="quiz_stats",
        description="Zeigt deine Quiz-Statistiken"
    )
    @app_commands.describe(
        user="User dessen Statistiken angezeigt werden sollen (optional)"
    )
    @error_handler
    async def quiz_stats(self, interaction: discord.Interaction, user: Optional[discord.Member] = None):
        """Zeigt Quiz-Statistiken eines Users"""
        target_user = user or interaction.user
        user_id_str = str(target_user.id)
        
        if user_id_str not in self.user_stats:
            await interaction.response.send_message(
                embed=create_error_embed(
                    f"{'Du hast' if target_user == interaction.user else f'{target_user.display_name} hat'} noch keine Quiz-Statistiken!"
                ),
                ephemeral=True
            )
            return
        
        stats = self.user_stats[user_id_str]
        
        # Berechne Durchschnitte
        accuracy = (stats['correct_answers'] / stats['total_questions']) * 100 if stats['total_questions'] > 0 else 0
        avg_time = stats['total_time'] / stats['total_quizzes'] if stats['total_quizzes'] > 0 else 0
        
        # Beste Kategorie
        best_category = "Keine"
        best_accuracy = 0
        
        for category, cat_stats in stats['categories'].items():
            if cat_stats['questions'] > 0:
                cat_accuracy = (cat_stats['correct'] / cat_stats['questions']) * 100
                if cat_accuracy > best_accuracy:
                    best_accuracy = cat_accuracy
                    best_category = category.title()
        
        embed = create_embed(
            title=f"📊 Quiz-Statistiken - {target_user.display_name}",
            color=discord.Color.blue(),
            thumbnail=target_user.display_avatar.url,
            fields=[
                {"name": "🎯 Gesamt", "value": f"Quizzes: {stats['total_quizzes']}\nFragen: {stats['total_questions']}\nRichtig: {stats['correct_answers']}", "inline": True},
                {"name": "📈 Durchschnitt", "value": f"Genauigkeit: {accuracy:.1f}%\nZeit/Quiz: {avg_time:.1f}s", "inline": True},
                {"name": "🏆 Beste Kategorie", "value": f"{best_category}\n({best_accuracy:.1f}%)", "inline": True}
            ]
        )
        
        # Kategorie-Details
        if stats['categories']:
            category_text = []
            for category, cat_stats in stats['categories'].items():
                if cat_stats['questions'] > 0:
                    cat_accuracy = (cat_stats['correct'] / cat_stats['questions']) * 100
                    category_text.append(f"**{category.title()}:** {cat_stats['correct']}/{cat_stats['questions']} ({cat_accuracy:.1f}%)")
            
            if category_text:
                embed.add_field(
                    name="📚 Kategorien",
                    value="\n".join(category_text[:5]),  # Limitiere auf 5
                    inline=False
                )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "quiz_stats", True)
    
    @app_commands.command(
        name="quiz_categories",
        description="Zeigt alle verfügbaren Quiz-Kategorien"
    )
    @error_handler
    async def quiz_categories(self, interaction: discord.Interaction):
        """Zeigt verfügbare Quiz-Kategorien"""
        if not self.quiz_data:
            await interaction.response.send_message(
                embed=create_error_embed("Keine Quiz-Kategorien verfügbar!"),
                ephemeral=True
            )
            return
        
        category_info = []
        for category, questions in self.quiz_data.items():
            category_info.append(f"**{category.title()}** - {len(questions)} Fragen")
        
        embed = create_embed(
            title="📚 Quiz-Kategorien",
            description="\n".join(category_info),
            color=discord.Color.blue(),
            fields=[
                {"name": "💡 Verwendung", "value": "`/quiz <kategorie>` - Quiz in spezifischer Kategorie\n`/quiz` - Zufällige Kategorie", "inline": False}
            ]
        )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "quiz_categories", True)
    
    @app_commands.command(
        name="quiz_stop",
        description="Stoppt das aktuelle Quiz"
    )
    @error_handler
    async def stop_quiz(self, interaction: discord.Interaction):
        """Stoppt das aktuelle Quiz"""
        if interaction.channel.id not in self.active_sessions:
            await interaction.response.send_message(
                embed=create_error_embed("In diesem Channel läuft kein Quiz!"),
                ephemeral=True
            )
            return
        
        session = self.active_sessions[interaction.channel.id]
        
        # Nur der Quiz-Starter oder Admins können stoppen
        if interaction.user.id != session.user_id:
            # Prüfe Admin-Rechte (vereinfacht)
            if not interaction.user.guild_permissions.administrator:
                await interaction.response.send_message(
                    embed=create_error_embed("Nur der Quiz-Starter oder Admins können das Quiz stoppen!"),
                    ephemeral=True
                )
                return
        
        # Entferne Session
        del self.active_sessions[interaction.channel.id]
        
        embed = create_success_embed(
            f"🛑 Quiz gestoppt!\n\nBisheriger Score: {session.score}/{session.current_question}"
        )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "quiz_stop", True)
    
    # Autocomplete für Kategorien
    @start_quiz.autocomplete('kategorie')
    async def category_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        """Autocomplete für Quiz-Kategorien"""
        categories = list(self.quiz_data.keys())
        
        if current:
            filtered_categories = [cat for cat in categories if current.lower() in cat.lower()]
        else:
            filtered_categories = categories
        
        return [
            app_commands.Choice(name=cat.title(), value=cat)
            for cat in filtered_categories[:25]
        ]


async def setup(bot: commands.Bot):
    """Setup-Funktion für das Cog"""
    await bot.add_cog(QuizCog(bot))