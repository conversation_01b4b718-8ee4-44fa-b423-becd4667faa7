# MongoDB Migration Guide

## Übersicht
Dieses Skript migriert alle lokalen JSON-Daten des Buttergolem Discord Bots zu MongoDB Atlas. Es liest alle vorhandenen Dateien aus und strukturiert sie in Collections für die zukünftige Nutzung.

## Vorbereitung

### 1. Dependencies installieren
```bash
pip install -r requirements-migration.txt
```

### 2. MongoDB Verbindung sicherstellen
- Die Verbindung zur MongoDB Atlas Datenbank ist bereits im Skript konfiguriert
- Stelle sicher, dass die Datenbank online ist und erreichbar

## Migration ausführen

### Einfache Ausführung
```bash
python migrate_to_mongodb.py
```

### Was wird migriert?

#### 1. Drachigotchi-Daten
- **Quelle**: `data/drachigotchis.json`
- **Collection**: `drachigotchis`
- **Felder**: user_id, name, level, exp, health, hunger, happiness, energy, money, fame, location, inventory, equipment, skills, achievements

#### 2. User Memories
- **Quelle**: `data/memories/*.json`
- **Collection**: `memories`
- **Felder**: user_id, user_info, important_facts, conversation_history

#### 3. Statistiken
- **Quelle**: `data/stats.json`, `src/ai_chatbot/logs/stats.json`
- **Collection**: `statistics`
- **Felder**: source, data, migrated_at

#### 4. AI-Chatbot & Basis KI-Daten
- **Collection**: `ai_chat_history`
- **Quelle**: `data/ai_chatbot.json`, `data/quotes.json`, `data/names.json`, `data/questions.json`, `data/cringe.json`
- **Felder**: type, data, migrated_at

#### 4a. Drachenlord KI-Datenbank
- **Collection**: `drache_ki_data`
- **Quelle**: `src/ki/` - ALLE Drachenlord-spezifischen JSON-Dateien:
  - `drache.json`, `drache_copy.json`, `drache_adjectives.json`
  - `drache_bio.json`, `drache_config.json`, `drache_controversies_2024.json`
  - `drache_events_2024_2025.json`, `drache_examples.json`
  - `drache_future_plans_2025.json`, `drache_knowledge.json`, `drache_lore.json`
  - `drache_messages.json`, `drache_opinions_2024.json`, `drache_people.json`
  - `drache_phrases_2024.json`, `drache_reactions_2024.json`, `drache_style.json`
  - `drache_system_prompt.json`, `drache_topics.json`, `drache_zitate_2024.json`
- **Felder**: type (Dateiname), data (Inhalt), source_path (Pfad), migrated_at

#### 4b. ButterIQ Sessions
- **Collection**: `butteriq_sessions`
- **Quelle**: `src/ai_chatbot/logs/sessions.json`
- **Felder**: session_id, user_id, messages, created_at, updated_at

#### 4c. ButterIQ Benutzerdaten
- **Collection**: `butteriq_users`
- **Quelle**: `src/ai_chatbot/logs/{user_id}.json` (individuelle Benutzerdateien)
- **Felder**: user_id, conversations, preferences, created_at, updated_at

#### 5. Ban-System
- **Quelle**: `data/ban.json`, `data/disabled_users.json`
- **Collection**: `bans`
- **Felder**: source, bans, migrated_at

#### 6. Server-Daten
- **Collection**: `servers`
- **Felder**: server_id, server_name, configuration

## Collections-Struktur

Nach der Migration werden folgende Collections erstellt:

| Collection | Beschreibung |
|------------|--------------|
| `users` | Benutzer-Informationen |
| `servers` | Discord Server-Daten |
| `drachigotchis` | Drachigotchi-Spielstände |
| `ai_chat_history` | KI-Chat Verläufe |
| `memories` | User Memories |
| `statistics` | Bot-Statistiken |
| `sound_plays` | Sound-Wiedergaben |
| `quiz_results` | Quiz-Ergebnisse |
| `donations` | Spenden-Logs |
| `bans` | Ban-Informationen |
| `commands` | Command-Usage |
| `achievements` | User Achievements |

## Fehlerbehandlung

### Häufige Probleme

1. **Connection Error**
   - Überprüfe Internetverbindung
   - Stelle sicher, dass die MongoDB Atlas URL korrekt ist

2. **File Not Found**
   - Das Skript überspringt fehlende Dateien automatisch
   - Es werden Warnungen für jede fehlende Datei ausgegeben

3. **JSON Parse Error**
   - Überprüfe die JSON-Dateien auf Syntaxfehler
   - Das Skript gibt detaillierte Fehlermeldungen aus

## Nach der Migration

### 1. Verifizierung
Das Skript zeigt nach der Migration eine Übersicht mit allen migrierten Dokumenten an.

### 2. Indizes
Automatisch erstellte Indizes für optimale Performance:
- `users.user_id` (unique)
- `drachigotchis.user_id` (unique)
- `memories.user_id` (unique)

### 3. Nächste Schritte
- Die JSON-Dateien können als Backup beibehalten werden
- Zukünftige Daten werden direkt in MongoDB gespeichert
- Alte JSON-Dateien können nach erfolgreicher Migration gelöscht werden

## Beispiel-Nutzung in Code

Nach der Migration kannst du so auf die Daten zugreifen:

```python
from pymongo import MongoClient

client = MongoClient("mongodb+srv://nindscher:<EMAIL>/")
db = client["buttergolem1"]

# Drachigotchi eines Users abrufen
drachi = db.drachigotchis.find_one({"user_id": *********})

# Memory eines Users aktualisieren
db.memories.update_one(
    {"user_id": *********},
    {"$set": {"last_updated": datetime.now()}}
)
```

## Support

Bei Problemen:
1. Überprüfe die Log-Ausgabe des Skripts
2. Stelle sicher, dass alle Dateien lesbar sind
3. Prüfe die MongoDB Atlas Logs

## Backup

**Wichtig**: Erstelle vor der Migration ein Backup deiner JSON-Dateien!
```bash
cp -r data/ data_backup_$(date +%Y%m%d_%H%M%S)
```