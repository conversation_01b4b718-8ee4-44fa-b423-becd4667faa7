@echo off
color 0A
title Buttergolem MongoDB Migration

echo.
echo ================================================
echo    BUTTERGOLEM MONGODB MIGRATION
echo ================================================
echo.
echo Dieses Skript migriert alle lokalen Daten zu MongoDB
pause

echo.
echo Installiere Dependencies...
pip install -r requirements-migration.txt

echo.
echo Starte Migration...
python migrate_to_mongodb.py

echo.
echo Migration abgeschlossen!
echo.
echo Möchtest du die Verifizierung durchführen?
echo 1 = Ja, Verifizierung starten
echo 2 = Nein, beenden
set /p choice="Wähle Option (1/2): "

if "%choice%"=="1" (
    echo.
    echo Starte Verifizierung...
    python verify_migration.py
) else (
    echo.
    echo Beende...
)

echo.
echo Drücke eine Taste zum Beenden...
pause > nul