# -*- coding: utf-8 -*-
"""
Logging Utilities für ButterGolem

Enthält Funktionen für Discord-Channel-Logging und Setup.
"""

import discord
import logging
import os
from typing import Optional
from datetime import datetime

# Logging-Konfiguration aus Environment Variables
LOGGING_CHANNEL_ID = int(os.getenv('LOGGING_CHANNEL_ID', 0))

logger = logging.getLogger(__name__)


def setup_logging(level: int = logging.INFO) -> None:
    """Konfiguriert das Logging-System"""
    # Erstelle Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console Handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    
    # File Handler (falls gewünscht)
    try:
        os.makedirs('logs', exist_ok=True)
        file_handler = logging.FileHandler(
            f'logs/buttergolem_{datetime.now().strftime("%Y%m%d")}.log',
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
    except Exception as e:
        print(f"Warnung: Konnte Log-Datei nicht erstellen: {e}")
        file_handler = None
    
    # Root Logger konfigurieren
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Handler hinzufügen (falls noch nicht vorhanden)
    if not root_logger.handlers:
        root_logger.addHandler(console_handler)
        if file_handler:
            root_logger.addHandler(file_handler)
    
    # Discord.py Logger konfigurieren
    discord_logger = logging.getLogger('discord')
    discord_logger.setLevel(logging.WARNING)  # Weniger verbose


async def log_to_channel(
    bot: discord.Client,
    message: str,
    level: str = "INFO",
    embed: bool = True
) -> bool:
    """Sendet eine Log-Nachricht an den konfigurierten Discord-Channel"""
    if not LOGGING_CHANNEL_ID:
        logger.warning("Kein Logging-Channel konfiguriert")
        return False
    
    try:
        channel = bot.get_channel(LOGGING_CHANNEL_ID)
        if not channel:
            logger.error(f"Logging-Channel {LOGGING_CHANNEL_ID} nicht gefunden")
            return False
        
        if embed:
            # Erstelle Embed basierend auf Log-Level
            color_map = {
                "DEBUG": discord.Color.light_grey(),
                "INFO": discord.Color.blue(),
                "WARNING": discord.Color.orange(),
                "ERROR": discord.Color.red(),
                "CRITICAL": discord.Color.dark_red()
            }
            
            embed_obj = discord.Embed(
                title=f"{level} Log",
                description=message,
                color=color_map.get(level.upper(), discord.Color.blue()),
                timestamp=datetime.now()
            )
            embed_obj.set_footer(text="ButterGolem Logging")
            
            await channel.send(embed=embed_obj)
        else:
            # Einfache Text-Nachricht
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await channel.send(f"[{timestamp}] {level}: {message}")
        
        return True
        
    except discord.errors.Forbidden:
        logger.error("Keine Berechtigung zum Senden in Logging-Channel")
        return False
    except discord.errors.NotFound:
        logger.error("Logging-Channel nicht gefunden")
        return False
    except Exception as e:
        logger.error(f"Fehler beim Senden der Log-Nachricht: {e}")
        return False


async def log_command_usage(
    bot: discord.Client,
    interaction: discord.Interaction,
    command_name: str,
    success: bool = True
) -> None:
    """Loggt die Verwendung eines Commands"""
    user = interaction.user
    guild = interaction.guild
    
    status = "✅" if success else "❌"
    guild_name = guild.name if guild else "DM"
    
    message = (
        f"{status} Command `{command_name}` verwendet\n"
        f"**User:** {user.mention} ({user.name}#{user.discriminator})\n"
        f"**Server:** {guild_name}\n"
        f"**Channel:** {interaction.channel.mention if hasattr(interaction.channel, 'mention') else 'DM'}"
    )
    
    await log_to_channel(bot, message, "INFO")


async def log_error(
    bot: discord.Client,
    error: Exception,
    context: str = "Unbekannt"
) -> None:
    """Loggt einen Fehler"""
    message = (
        f"**Fehler in:** {context}\n"
        f"**Typ:** {type(error).__name__}\n"
        f"**Nachricht:** {str(error)}"
    )
    
    await log_to_channel(bot, message, "ERROR")


async def log_admin_action(
    bot: discord.Client,
    admin_user: discord.User,
    action: str,
    target: str = None,
    details: str = None
) -> None:
    """Loggt Admin-Aktionen"""
    message = (
        f"🔧 **Admin-Aktion:** {action}\n"
        f"**Admin:** {admin_user.mention} ({admin_user.name}#{admin_user.discriminator})"
    )
    
    if target:
        message += f"\n**Ziel:** {target}"
    
    if details:
        message += f"\n**Details:** {details}"
    
    await log_to_channel(bot, message, "WARNING")


async def log_guild_event(
    bot: discord.Client,
    event_type: str,
    guild: discord.Guild,
    details: str = None
) -> None:
    """Loggt Server-Events (Join/Leave etc.)"""
    message = (
        f"🏠 **Server-Event:** {event_type}\n"
        f"**Server:** {guild.name} (ID: {guild.id})"
    )
    
    if details:
        message += f"\n**Details:** {details}"
    
    await log_to_channel(bot, message, "INFO")


class DiscordLogHandler(logging.Handler):
    """Custom Log Handler der Logs an Discord sendet"""
    
    def __init__(self, bot: discord.Client):
        super().__init__()
        self.bot = bot
    
    def emit(self, record: logging.LogRecord) -> None:
        """Sendet Log-Record an Discord"""
        try:
            message = self.format(record)
            level = record.levelname
            
            # Async call in sync context - nur für kritische Fehler
            if level in ["ERROR", "CRITICAL"]:
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # Wenn Loop bereits läuft, Task erstellen
                        loop.create_task(log_to_channel(self.bot, message, level))
                    else:
                        # Wenn kein Loop läuft, neuen erstellen
                        loop.run_until_complete(log_to_channel(self.bot, message, level))
                except Exception:
                    pass  # Fehler beim Discord-Logging ignorieren
        except Exception:
            pass  # Fehler beim Discord-Logging ignorieren