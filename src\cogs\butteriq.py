# -*- coding: utf-8 -*-
"""
ButterIQ Cog für ButterGolem

Enthält KI-basierte Funktionen und intelligente Features.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
import random
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import re

from .utils.decorators import error_handler
from .utils.helpers import (
    create_embed, 
    create_error_embed, 
    create_success_embed,
    load_json_data,
    save_json_data,
    truncate_text
)
from .utils.logging_utils import log_command_usage

logger = logging.getLogger(__name__)


class ButterIQCog(commands.Cog, name="ButterIQ"):
    """KI-basierte Features für ButterGolem"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.conversation_history = {}
        self.ai_config = self.load_ai_config()
        self.responses = self.load_responses()
        self.learning_data = self.load_learning_data()
        logger.info("ButterIQ Cog geladen")
    
    def load_ai_config(self) -> dict:
        """Lädt AI-Konfiguration"""
        try:
            config = load_json_data('ai_config.json')
            if config:
                return config
            
            # Default-Konfiguration
            default_config = {
                "enabled": True,
                "max_conversation_length": 10,
                "response_chance": 0.1,  # 10% Chance auf automatische Antwort
                "learning_enabled": True,
                "personality": {
                    "humor_level": 0.7,
                    "helpfulness": 0.8,
                    "sass_level": 0.3,
                    "randomness": 0.5
                },
                "trigger_words": [
                    "buttergolem", "butter", "golem", "bot", "ki", "ai",
                    "hilfe", "help", "frage", "warum", "wie", "was"
                ],
                "blacklisted_channels": [],
                "max_response_length": 500
            }
            
            save_json_data('ai_config.json', default_config)
            return default_config
            
        except Exception as e:
            logger.error(f"Fehler beim Laden der AI-Konfiguration: {e}")
            return {}
    
    def load_responses(self) -> dict:
        """Lädt vordefinierte Antworten"""
        try:
            responses = load_json_data('ai_responses.json')
            if responses:
                return responses
            
            # Default-Antworten
            default_responses = {
                "greetings": [
                    "Hallo! 👋 Wie kann ich dir helfen?",
                    "Hey! 🧈 Was gibt's?",
                    "Moin! Schön dich zu sehen!",
                    "Hallo! Ich bin ButterGolem, dein butteriger Assistent! 🤖"
                ],
                "questions": {
                    "was": [
                        "Das ist eine interessante Frage! 🤔",
                        "Hmm, lass mich überlegen... 💭",
                        "Gute Frage! Darüber denke ich nach."
                    ],
                    "wie": [
                        "Das kommt darauf an! 🎯",
                        "Es gibt verschiedene Wege...",
                        "Lass uns das zusammen herausfinden!"
                    ],
                    "warum": [
                        "Weil das Leben manchmal seltsam ist! 🤷‍♂️",
                        "Das ist eine philosophische Frage...",
                        "Warum nicht? 😄"
                    ]
                },
                "compliments": [
                    "Danke! Du bist auch toll! 😊",
                    "Aww, das ist lieb von dir! 🥰",
                    "Du machst mich ganz verlegen! 😳",
                    "Zurück an dich! ✨"
                ],
                "insults": [
                    "Hey, das war nicht nett! 😢",
                    "Ich bin auch nur ein Bot mit Gefühlen! 🤖💔",
                    "Autsch! Das tat weh... 😭",
                    "Können wir nicht Freunde sein? 🥺"
                ],
                "confusion": [
                    "Ich verstehe nicht ganz... 🤔",
                    "Kannst du das nochmal erklären?",
                    "Das ist zu kompliziert für mein Butter-Gehirn! 🧈🧠",
                    "Hä? 😵‍💫"
                ],
                "help": [
                    "Gerne helfe ich dir! Verwende `/help` für alle Befehle.",
                    "Ich bin hier um zu helfen! Was brauchst du?",
                    "Lass uns das Problem lösen! 💪"
                ],
                "random": [
                    "Wusstest du, dass Butter bei 32°C schmilzt? 🧈",
                    "Ich träume manchmal von elektrischen Schafen... 🐑⚡",
                    "42 ist die Antwort auf alles! 🌌",
                    "Beep boop! 🤖",
                    "Ich liebe es, ein Bot zu sein! 💖",
                    "Pizza ist Leben! 🍕",
                    "Kaffee oder Tee? ☕"
                ]
            }
            
            save_json_data('ai_responses.json', default_responses)
            return default_responses
            
        except Exception as e:
            logger.error(f"Fehler beim Laden der AI-Antworten: {e}")
            return {}
    
    def load_learning_data(self) -> dict:
        """Lädt Lerndaten"""
        try:
            learning = load_json_data('ai_learning.json')
            return learning if learning else {
                "word_associations": {},
                "user_preferences": {},
                "conversation_patterns": {},
                "learned_responses": []
            }
        except Exception as e:
            logger.error(f"Fehler beim Laden der Lerndaten: {e}")
            return {}
    
    def save_learning_data(self) -> None:
        """Speichert Lerndaten"""
        try:
            save_json_data('ai_learning.json', self.learning_data)
        except Exception as e:
            logger.error(f"Fehler beim Speichern der Lerndaten: {e}")
    
    def analyze_message(self, message: str) -> dict:
        """Analysiert eine Nachricht"""
        message_lower = message.lower()
        
        analysis = {
            "sentiment": "neutral",
            "question_type": None,
            "contains_greeting": False,
            "contains_trigger": False,
            "is_question": False,
            "keywords": [],
            "emotion": "neutral"
        }
        
        # Grüße erkennen
        greetings = ["hallo", "hi", "hey", "moin", "guten", "servus"]
        analysis["contains_greeting"] = any(greeting in message_lower for greeting in greetings)
        
        # Trigger-Wörter
        trigger_words = self.ai_config.get("trigger_words", [])
        analysis["contains_trigger"] = any(trigger in message_lower for trigger in trigger_words)
        
        # Fragen erkennen
        question_starters = ["was", "wie", "warum", "wer", "wo", "wann", "welche"]
        for starter in question_starters:
            if message_lower.startswith(starter) or f" {starter} " in message_lower:
                analysis["question_type"] = starter
                analysis["is_question"] = True
                break
        
        if "?" in message:
            analysis["is_question"] = True
        
        # Sentiment-Analyse (vereinfacht)
        positive_words = ["gut", "toll", "super", "awesome", "cool", "danke", "liebe", "mag"]
        negative_words = ["schlecht", "doof", "blöd", "hasse", "nervig", "dumm", "scheiße"]
        
        positive_count = sum(1 for word in positive_words if word in message_lower)
        negative_count = sum(1 for word in negative_words if word in message_lower)
        
        if positive_count > negative_count:
            analysis["sentiment"] = "positive"
            analysis["emotion"] = "happy"
        elif negative_count > positive_count:
            analysis["sentiment"] = "negative"
            analysis["emotion"] = "sad"
        
        # Keywords extrahieren
        words = re.findall(r'\b\w+\b', message_lower)
        analysis["keywords"] = [word for word in words if len(word) > 3]
        
        return analysis
    
    def generate_response(self, message: str, user_id: int, analysis: dict) -> Optional[str]:
        """Generiert eine Antwort basierend auf der Nachricht"""
        personality = self.ai_config.get("personality", {})
        
        # Grüße
        if analysis["contains_greeting"]:
            return random.choice(self.responses.get("greetings", ["Hallo!"]))
        
        # Fragen
        if analysis["is_question"] and analysis["question_type"]:
            question_responses = self.responses.get("questions", {}).get(analysis["question_type"], [])
            if question_responses:
                return random.choice(question_responses)
        
        # Sentiment-basierte Antworten
        if analysis["sentiment"] == "positive":
            return random.choice(self.responses.get("compliments", ["Danke!"]))
        elif analysis["sentiment"] == "negative":
            return random.choice(self.responses.get("insults", ["Oh nein!"]))
        
        # Hilfe-Anfragen
        help_keywords = ["hilfe", "help", "helfen", "problem"]
        if any(keyword in message.lower() for keyword in help_keywords):
            return random.choice(self.responses.get("help", ["Wie kann ich helfen?"]))
        
        # Zufällige Antworten
        if random.random() < personality.get("randomness", 0.5):
            return random.choice(self.responses.get("random", ["Interessant!"]))
        
        # Verwirrung
        return random.choice(self.responses.get("confusion", ["Hmm?"]))
    
    def should_respond(self, message: discord.Message, analysis: dict) -> bool:
        """Entscheidet ob der Bot antworten soll"""
        if not self.ai_config.get("enabled", True):
            return False
        
        # Nicht auf eigene Nachrichten antworten
        if message.author == self.bot.user:
            return False
        
        # Nicht auf andere Bots antworten
        if message.author.bot:
            return False
        
        # Blacklisted Channels
        blacklisted = self.ai_config.get("blacklisted_channels", [])
        if message.channel.id in blacklisted:
            return False
        
        # Immer antworten wenn Bot erwähnt wird
        if self.bot.user in message.mentions:
            return True
        
        # Antworten wenn Trigger-Wörter enthalten sind
        if analysis["contains_trigger"]:
            return True
        
        # Antworten auf Grüße
        if analysis["contains_greeting"]:
            return random.random() < 0.7  # 70% Chance
        
        # Antworten auf Fragen
        if analysis["is_question"]:
            return random.random() < 0.5  # 50% Chance
        
        # Zufällige Antworten
        response_chance = self.ai_config.get("response_chance", 0.1)
        return random.random() < response_chance
    
    def learn_from_message(self, message: discord.Message, analysis: dict) -> None:
        """Lernt aus einer Nachricht"""
        if not self.ai_config.get("learning_enabled", True):
            return
        
        user_id = str(message.author.id)
        
        # User-Präferenzen lernen
        if user_id not in self.learning_data["user_preferences"]:
            self.learning_data["user_preferences"][user_id] = {
                "favorite_words": {},
                "sentiment_history": [],
                "interaction_count": 0
            }
        
        user_prefs = self.learning_data["user_preferences"][user_id]
        user_prefs["interaction_count"] += 1
        user_prefs["sentiment_history"].append(analysis["sentiment"])
        
        # Nur die letzten 50 Sentiments behalten
        if len(user_prefs["sentiment_history"]) > 50:
            user_prefs["sentiment_history"] = user_prefs["sentiment_history"][-50:]
        
        # Wort-Assoziationen lernen
        for keyword in analysis["keywords"]:
            if keyword not in self.learning_data["word_associations"]:
                self.learning_data["word_associations"][keyword] = {
                    "count": 0,
                    "sentiments": [],
                    "users": []
                }
            
            word_data = self.learning_data["word_associations"][keyword]
            word_data["count"] += 1
            word_data["sentiments"].append(analysis["sentiment"])
            
            if user_id not in word_data["users"]:
                word_data["users"].append(user_id)
            
            # Limitiere Daten
            if len(word_data["sentiments"]) > 100:
                word_data["sentiments"] = word_data["sentiments"][-100:]
        
        self.save_learning_data()
    
    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        """Reagiert auf Nachrichten"""
        try:
            # Analysiere Nachricht
            analysis = self.analyze_message(message.content)
            
            # Lerne aus der Nachricht
            self.learn_from_message(message, analysis)
            
            # Entscheide ob geantwortet werden soll
            if not self.should_respond(message, analysis):
                return
            
            # Generiere Antwort
            response = self.generate_response(message.content, message.author.id, analysis)
            
            if response:
                # Typing-Indikator für Realismus
                async with message.channel.typing():
                    await asyncio.sleep(random.uniform(0.5, 2.0))
                
                await message.reply(response, mention_author=False)
                
        except Exception as e:
            logger.error(f"Fehler in ButterIQ on_message: {e}")
    
    @app_commands.command(
        name="ask",
        description="Stelle ButterIQ eine Frage"
    )
    @app_commands.describe(
        frage="Deine Frage an ButterIQ"
    )
    @error_handler
    async def ask(self, interaction: discord.Interaction, frage: str):
        """Beantwortet eine Frage mit ButterIQ"""
        # Analysiere Frage
        analysis = self.analyze_message(frage)
        
        # Generiere Antwort
        response = self.generate_response(frage, interaction.user.id, analysis)
        
        if not response:
            response = "Das ist eine interessante Frage! Leider weiß ich darauf keine Antwort. 🤔"
        
        embed = create_embed(
            title="🧠 ButterIQ",
            description=f"**Frage:** {frage}\n\n**Antwort:** {response}",
            color=discord.Color.purple()
        )
        
        # Füge Analyse-Info hinzu (für Debug)
        if analysis["sentiment"] != "neutral":
            embed.set_footer(text=f"Sentiment: {analysis['sentiment']} | Emotion: {analysis['emotion']}")
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "ask", True)
    
    @app_commands.command(
        name="ai_stats",
        description="Zeigt ButterIQ Statistiken"
    )
    @error_handler
    async def ai_stats(self, interaction: discord.Interaction):
        """Zeigt AI-Statistiken"""
        # Berechne Statistiken
        total_words = len(self.learning_data.get("word_associations", {}))
        total_users = len(self.learning_data.get("user_preferences", {}))
        
        # Häufigste Wörter
        word_associations = self.learning_data.get("word_associations", {})
        top_words = sorted(
            word_associations.items(),
            key=lambda x: x[1].get("count", 0),
            reverse=True
        )[:5]
        
        # Sentiment-Verteilung
        all_sentiments = []
        for user_prefs in self.learning_data.get("user_preferences", {}).values():
            all_sentiments.extend(user_prefs.get("sentiment_history", []))
        
        sentiment_counts = {
            "positive": all_sentiments.count("positive"),
            "negative": all_sentiments.count("negative"),
            "neutral": all_sentiments.count("neutral")
        }
        
        embed = create_embed(
            title="🧠 ButterIQ Statistiken",
            color=discord.Color.purple()
        )
        
        embed.add_field(
            name="📊 Allgemein",
            value=f"Gelernte Wörter: {total_words}\n"
                  f"Bekannte User: {total_users}\n"
                  f"Status: {'🟢 Aktiv' if self.ai_config.get('enabled') else '🔴 Deaktiviert'}",
            inline=True
        )
        
        # Häufigste Wörter
        if top_words:
            words_text = "\n".join([f"{word}: {data['count']}x" for word, data in top_words])
            embed.add_field(
                name="🔤 Häufigste Wörter",
                value=words_text,
                inline=True
            )
        
        # Sentiment-Verteilung
        if any(sentiment_counts.values()):
            total_sentiments = sum(sentiment_counts.values())
            sentiment_text = "\n".join([
                f"😊 Positiv: {sentiment_counts['positive']} ({sentiment_counts['positive']/total_sentiments*100:.1f}%)",
                f"😐 Neutral: {sentiment_counts['neutral']} ({sentiment_counts['neutral']/total_sentiments*100:.1f}%)",
                f"😞 Negativ: {sentiment_counts['negative']} ({sentiment_counts['negative']/total_sentiments*100:.1f}%)"
            ])
            
            embed.add_field(
                name="💭 Sentiment-Verteilung",
                value=sentiment_text,
                inline=False
            )
        
        # Konfiguration
        personality = self.ai_config.get("personality", {})
        config_text = "\n".join([
            f"Humor: {personality.get('humor_level', 0)*100:.0f}%",
            f"Hilfsbereitschaft: {personality.get('helpfulness', 0)*100:.0f}%",
            f"Sass: {personality.get('sass_level', 0)*100:.0f}%",
            f"Zufälligkeit: {personality.get('randomness', 0)*100:.0f}%"
        ])
        
        embed.add_field(
            name="🎭 Persönlichkeit",
            value=config_text,
            inline=True
        )
        
        embed.set_footer(text="ButterIQ lernt aus euren Unterhaltungen! 🧈🤖")
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "ai_stats", True)
    
    @app_commands.command(
        name="ai_toggle",
        description="[ADMIN] Aktiviert/Deaktiviert ButterIQ"
    )
    @error_handler
    async def ai_toggle(self, interaction: discord.Interaction):
        """Schaltet ButterIQ ein/aus (Admin only)"""
        # Vereinfachte Admin-Prüfung
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                embed=create_error_embed("Dieser Befehl ist nur für Administratoren!"),
                ephemeral=True
            )
            return
        
        # Toggle Status
        current_status = self.ai_config.get("enabled", True)
        self.ai_config["enabled"] = not current_status
        
        # Speichere Konfiguration
        try:
            save_json_data('ai_config.json', self.ai_config)
        except Exception as e:
            logger.error(f"Fehler beim Speichern der AI-Konfiguration: {e}")
        
        status_text = "aktiviert" if self.ai_config["enabled"] else "deaktiviert"
        status_emoji = "🟢" if self.ai_config["enabled"] else "🔴"
        
        embed = create_success_embed(
            f"{status_emoji} ButterIQ wurde {status_text}!"
        )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "ai_toggle", True)
    
    @app_commands.command(
        name="personality",
        description="Zeigt die aktuelle ButterIQ Persönlichkeit"
    )
    @error_handler
    async def personality(self, interaction: discord.Interaction):
        """Zeigt ButterIQ Persönlichkeit"""
        personality = self.ai_config.get("personality", {})
        
        embed = create_embed(
            title="🎭 ButterIQ Persönlichkeit",
            description="So bin ich konfiguriert!",
            color=discord.Color.purple()
        )
        
        # Persönlichkeits-Balken
        def create_bar(value: float, length: int = 10) -> str:
            filled = int(value * length)
            return "█" * filled + "░" * (length - filled)
        
        personality_fields = [
            ("😄 Humor", personality.get("humor_level", 0.7)),
            ("🤝 Hilfsbereitschaft", personality.get("helpfulness", 0.8)),
            ("😏 Sass", personality.get("sass_level", 0.3)),
            ("🎲 Zufälligkeit", personality.get("randomness", 0.5))
        ]
        
        for name, value in personality_fields:
            bar = create_bar(value)
            percentage = int(value * 100)
            embed.add_field(
                name=name,
                value=f"{bar} {percentage}%",
                inline=False
            )
        
        # Zusätzliche Infos
        embed.add_field(
            name="⚙️ Einstellungen",
            value=f"Antwort-Chance: {self.ai_config.get('response_chance', 0.1)*100:.0f}%\n"
                  f"Max. Unterhaltungslänge: {self.ai_config.get('max_conversation_length', 10)}\n"
                  f"Lernen: {'🟢 An' if self.ai_config.get('learning_enabled') else '🔴 Aus'}",
            inline=False
        )
        
        embed.set_footer(text="Meine Persönlichkeit entwickelt sich durch eure Nachrichten! 🧈")
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "personality", True)


async def setup(bot: commands.Bot):
    """Setup-Funktion für das Cog"""
    await bot.add_cog(ButterIQCog(bot))