# -*- coding: utf-8 -*-
"""
Contact Cog für ButterGolem

Enthält Kontakt- und Support-Funktionen.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
from typing import Optional, List
from datetime import datetime

from .utils.decorators import error_handler
from .utils.helpers import (
    create_embed, 
    create_error_embed, 
    create_success_embed,
    load_json_data,
    save_json_data,
    truncate_text
)
from .utils.logging_utils import log_command_usage, log_to_channel

logger = logging.getLogger(__name__)


class ContactCog(commands.Cog, name="Contact"):
    """Kontakt- und Support-System für ButterGolem"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.feedback_data = self.load_feedback_data()
        self.support_config = self.load_support_config()
        logger.info("Contact Cog geladen")
    
    def load_feedback_data(self) -> List[dict]:
        """<PERSON>ädt gespeicherte Feedback-Daten"""
        try:
            feedback = load_json_data('feedback.json')
            return feedback if feedback else []
        except Exception as e:
            logger.error(f"Fehler beim <PERSON> der <PERSON>back-Daten: {e}")
            return []
    
    def save_feedback_data(self) -> None:
        """Speichert Feedback-Daten"""
        try:
            save_json_data('feedback.json', self.feedback_data)
        except Exception as e:
            logger.error(f"Fehler beim Speichern der Feedback-Daten: {e}")
    
    def load_support_config(self) -> dict:
        """Lädt Support-Konfiguration"""
        try:
            config = load_json_data('support_config.json')
            if config:
                return config
            
            # Default-Konfiguration
            default_config = {
                "support_channel_id": None,
                "feedback_channel_id": None,
                "auto_response": True,
                "support_role_id": None,
                "max_feedback_length": 1000,
                "contact_info": {
                    "discord_server": "https://discord.gg/example",
                    "github": "https://github.com/example/buttergolem",
                    "email": "<EMAIL>"
                }
            }
            
            save_json_data('support_config.json', default_config)
            return default_config
            
        except Exception as e:
            logger.error(f"Fehler beim Laden der Support-Konfiguration: {e}")
            return {}
    
    def save_support_config(self) -> None:
        """Speichert Support-Konfiguration"""
        try:
            save_json_data('support_config.json', self.support_config)
        except Exception as e:
            logger.error(f"Fehler beim Speichern der Support-Konfiguration: {e}")
    
    @app_commands.command(
        name="feedback",
        description="Sende Feedback an die Entwickler"
    )
    @app_commands.describe(
        nachricht="Dein Feedback oder Vorschlag",
        typ="Art des Feedbacks"
    )
    @error_handler
    async def feedback(self, interaction: discord.Interaction, nachricht: str, typ: Optional[str] = "Allgemein"):
        """Sendet Feedback an die Entwickler"""
        # Validiere Nachrichtenlänge
        max_length = self.support_config.get('max_feedback_length', 1000)
        if len(nachricht) > max_length:
            await interaction.response.send_message(
                embed=create_error_embed(
                    f"Feedback ist zu lang! Maximum: {max_length} Zeichen, deine Nachricht: {len(nachricht)} Zeichen."
                ),
                ephemeral=True
            )
            return
        
        # Erstelle Feedback-Eintrag
        feedback_entry = {
            "id": len(self.feedback_data) + 1,
            "user_id": interaction.user.id,
            "username": str(interaction.user),
            "guild_id": interaction.guild.id if interaction.guild else None,
            "guild_name": interaction.guild.name if interaction.guild else "DM",
            "type": typ,
            "message": nachricht,
            "timestamp": datetime.now().isoformat(),
            "status": "neu"
        }
        
        self.feedback_data.append(feedback_entry)
        self.save_feedback_data()
        
        # Sende Bestätigung an User
        embed = create_success_embed(
            f"📝 Feedback erhalten!\n\n"
            f"**ID:** #{feedback_entry['id']}\n"
            f"**Typ:** {typ}\n\n"
            f"Vielen Dank für dein Feedback! Wir werden es so schnell wie möglich bearbeiten."
        )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)
        
        # Sende an Feedback-Channel (falls konfiguriert)
        feedback_channel_id = self.support_config.get('feedback_channel_id')
        if feedback_channel_id:
            try:
                feedback_channel = self.bot.get_channel(feedback_channel_id)
                if feedback_channel:
                    feedback_embed = create_embed(
                        title=f"📝 Neues Feedback #{feedback_entry['id']}",
                        description=f"**Von:** {interaction.user.mention} ({interaction.user})\n"
                                  f"**Server:** {interaction.guild.name if interaction.guild else 'DM'}\n"
                                  f"**Typ:** {typ}\n\n"
                                  f"**Nachricht:**\n{nachricht}",
                        color=discord.Color.blue(),
                        timestamp=datetime.now()
                    )
                    
                    await feedback_channel.send(embed=feedback_embed)
            except Exception as e:
                logger.error(f"Fehler beim Senden des Feedbacks an Channel: {e}")
        
        await log_command_usage(self.bot, interaction, "feedback", True)
    
    @app_commands.command(
        name="bugreport",
        description="Melde einen Bug"
    )
    @app_commands.describe(
        beschreibung="Beschreibung des Bugs",
        schritte="Schritte zur Reproduktion (optional)"
    )
    @error_handler
    async def bugreport(self, interaction: discord.Interaction, beschreibung: str, schritte: Optional[str] = None):
        """Meldet einen Bug"""
        # Erstelle detaillierten Bug-Report
        bug_message = f"**Bug-Beschreibung:**\n{beschreibung}"
        
        if schritte:
            bug_message += f"\n\n**Schritte zur Reproduktion:**\n{schritte}"
        
        # Füge System-Informationen hinzu
        bug_message += f"\n\n**System-Info:**\n"
        bug_message += f"Server: {interaction.guild.name if interaction.guild else 'DM'}\n"
        bug_message += f"Channel: {interaction.channel.name if hasattr(interaction.channel, 'name') else 'DM'}\n"
        bug_message += f"Bot Version: {getattr(self.bot, 'version', 'Unbekannt')}\n"
        bug_message += f"Discord.py Version: {discord.__version__}"
        
        # Sende als Feedback mit Typ "Bug"
        await self.feedback(
            interaction, 
            bug_message, 
            "Bug Report"
        )
    
    @app_commands.command(
        name="support",
        description="Zeigt Support-Informationen und Kontaktmöglichkeiten"
    )
    @error_handler
    async def support(self, interaction: discord.Interaction):
        """Zeigt Support-Informationen"""
        contact_info = self.support_config.get('contact_info', {})
        
        embed = create_embed(
            title="🆘 Support & Kontakt",
            description="Brauchst du Hilfe? Hier findest du alle Kontaktmöglichkeiten!",
            color=discord.Color.blue()
        )
        
        # Commands
        embed.add_field(
            name="🤖 Bot-Commands",
            value="`/help` - Alle verfügbaren Befehle\n"
                  "`/feedback` - Feedback senden\n"
                  "`/bugreport` - Bug melden\n"
                  "`/info` - Bot-Informationen",
            inline=False
        )
        
        # Kontakt-Informationen
        contact_text = []
        
        if contact_info.get('discord_server'):
            contact_text.append(f"🔗 [Discord Server]({contact_info['discord_server']})")
        
        if contact_info.get('github'):
            contact_text.append(f"📁 [GitHub Repository]({contact_info['github']})")
        
        if contact_info.get('email'):
            contact_text.append(f"📧 Email: {contact_info['email']}")
        
        if contact_text:
            embed.add_field(
                name="📞 Kontakt",
                value="\n".join(contact_text),
                inline=False
            )
        
        # FAQ
        embed.add_field(
            name="❓ Häufige Fragen",
            value="**Bot reagiert nicht?**\n"
                  "→ Prüfe die Bot-Berechtigungen\n\n"
                  "**Befehle funktionieren nicht?**\n"
                  "→ Verwende `/help` für eine Liste aller Befehle\n\n"
                  "**Probleme mit Audio?**\n"
                  "→ Bot muss in einem Voice Channel sein",
            inline=False
        )
        
        # Support-Channel Info
        support_channel_id = self.support_config.get('support_channel_id')
        if support_channel_id:
            support_channel = self.bot.get_channel(support_channel_id)
            if support_channel:
                embed.add_field(
                    name="💬 Support-Channel",
                    value=f"Für direkten Support besuche {support_channel.mention}",
                    inline=False
                )
        
        embed.set_footer(text="Wir helfen gerne! 🎯")
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "support", True)
    
    @app_commands.command(
        name="invite",
        description="Zeigt den Einladungslink für den Bot"
    )
    @error_handler
    async def invite(self, interaction: discord.Interaction):
        """Zeigt Bot-Einladungslink"""
        # Berechne benötigte Permissions
        permissions = discord.Permissions(
            send_messages=True,
            embed_links=True,
            attach_files=True,
            read_message_history=True,
            use_slash_commands=True,
            connect=True,
            speak=True,
            use_voice_activation=True,
            manage_messages=True,
            add_reactions=True
        )
        
        invite_url = discord.utils.oauth_url(
            self.bot.user.id,
            permissions=permissions,
            scopes=['bot', 'applications.commands']
        )
        
        embed = create_embed(
            title="🎉 ButterGolem einladen",
            description=f"Lade {self.bot.user.mention} auf deinen Server ein!",
            color=discord.Color.green()
        )
        
        embed.add_field(
            name="🔗 Einladungslink",
            value=f"[Bot einladen]({invite_url})",
            inline=False
        )
        
        embed.add_field(
            name="⚙️ Benötigte Berechtigungen",
            value="• Nachrichten senden\n"
                  "• Embeds verwenden\n"
                  "• Dateien anhängen\n"
                  "• Voice Channel beitreten\n"
                  "• Sprechen\n"
                  "• Slash Commands verwenden",
            inline=False
        )
        
        embed.add_field(
            name="🎯 Features",
            value="• Lustige Befehle & Memes\n"
                  "• Audio-Clips abspielen\n"
                  "• Quiz-System\n"
                  "• Admin-Tools\n"
                  "• Und vieles mehr!",
            inline=False
        )
        
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)
        embed.set_footer(text="Danke, dass du ButterGolem verwendest! 🧈")
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "invite", True)
    
    @app_commands.command(
        name="changelog",
        description="Zeigt die neuesten Änderungen und Updates"
    )
    @error_handler
    async def changelog(self, interaction: discord.Interaction):
        """Zeigt Changelog"""
        # Lade Changelog-Daten
        try:
            changelog_data = load_json_data('changelog.json')
            if not changelog_data:
                # Erstelle Beispiel-Changelog
                changelog_data = {
                    "current_version": "2.0.0",
                    "releases": [
                        {
                            "version": "2.0.0",
                            "date": "2024-01-15",
                            "title": "Große Refaktorierung",
                            "changes": [
                                "🔄 Komplette Code-Refaktorierung mit Cogs",
                                "✨ Neues Quiz-System",
                                "🎵 Verbesserte Audio-Features",
                                "🛠️ Bessere Admin-Tools",
                                "📊 Erweiterte Statistiken"
                            ],
                            "fixes": [
                                "🐛 Behoben: Audio-Probleme",
                                "🐛 Behoben: Memory Leaks",
                                "🐛 Behoben: Slash Command Bugs"
                            ]
                        },
                        {
                            "version": "1.5.2",
                            "date": "2024-01-01",
                            "title": "Hotfix",
                            "changes": [
                                "🔧 Performance-Verbesserungen"
                            ],
                            "fixes": [
                                "🐛 Behoben: Crash bei großen Servern"
                            ]
                        }
                    ]
                }
                save_json_data('changelog.json', changelog_data)
        except Exception as e:
            logger.error(f"Fehler beim Laden des Changelogs: {e}")
            await interaction.response.send_message(
                embed=create_error_embed("Changelog konnte nicht geladen werden!"),
                ephemeral=True
            )
            return
        
        current_version = changelog_data.get('current_version', 'Unbekannt')
        releases = changelog_data.get('releases', [])
        
        if not releases:
            await interaction.response.send_message(
                embed=create_error_embed("Keine Changelog-Einträge verfügbar!"),
                ephemeral=True
            )
            return
        
        # Zeige neueste Version
        latest_release = releases[0]
        
        embed = create_embed(
            title=f"📋 Changelog - v{current_version}",
            description=f"**{latest_release['title']}** ({latest_release['date']})",
            color=discord.Color.blue()
        )
        
        # Neue Features
        if latest_release.get('changes'):
            changes_text = "\n".join(latest_release['changes'][:10])  # Limitiere auf 10
            embed.add_field(
                name="✨ Neue Features",
                value=changes_text,
                inline=False
            )
        
        # Bug Fixes
        if latest_release.get('fixes'):
            fixes_text = "\n".join(latest_release['fixes'][:5])  # Limitiere auf 5
            embed.add_field(
                name="🔧 Bug Fixes",
                value=fixes_text,
                inline=False
            )
        
        # Weitere Versionen
        if len(releases) > 1:
            other_versions = [f"v{r['version']} - {r['title']}" for r in releases[1:3]]
            embed.add_field(
                name="📚 Frühere Versionen",
                value="\n".join(other_versions),
                inline=False
            )
        
        embed.set_footer(text="Verwende /feedback um Vorschläge zu machen!")
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "changelog", True)
    
    # Admin-Commands für Feedback-Management
    @app_commands.command(
        name="feedback_list",
        description="[ADMIN] Zeigt alle Feedback-Einträge"
    )
    @app_commands.describe(
        status="Filter nach Status",
        typ="Filter nach Typ"
    )
    @error_handler
    async def feedback_list(self, interaction: discord.Interaction, status: Optional[str] = None, typ: Optional[str] = None):
        """Zeigt Feedback-Liste (Admin only)"""
        # Vereinfachte Admin-Prüfung
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                embed=create_error_embed("Dieser Befehl ist nur für Administratoren!"),
                ephemeral=True
            )
            return
        
        if not self.feedback_data:
            await interaction.response.send_message(
                embed=create_error_embed("Keine Feedback-Einträge vorhanden!"),
                ephemeral=True
            )
            return
        
        # Filtere Feedback
        filtered_feedback = self.feedback_data
        
        if status:
            filtered_feedback = [f for f in filtered_feedback if f.get('status', '').lower() == status.lower()]
        
        if typ:
            filtered_feedback = [f for f in filtered_feedback if f.get('type', '').lower() == typ.lower()]
        
        if not filtered_feedback:
            await interaction.response.send_message(
                embed=create_error_embed("Keine Feedback-Einträge mit diesen Filtern gefunden!"),
                ephemeral=True
            )
            return
        
        # Erstelle Übersicht
        embed = create_embed(
            title="📝 Feedback-Übersicht",
            description=f"Zeige {len(filtered_feedback)} von {len(self.feedback_data)} Einträgen",
            color=discord.Color.blue()
        )
        
        # Zeige neueste 10 Einträge
        recent_feedback = sorted(filtered_feedback, key=lambda x: x.get('timestamp', ''), reverse=True)[:10]
        
        feedback_text = []
        for feedback in recent_feedback:
            status_emoji = {
                'neu': '🆕',
                'bearbeitet': '⏳',
                'erledigt': '✅',
                'abgelehnt': '❌'
            }.get(feedback.get('status', 'neu'), '❓')
            
            feedback_text.append(
                f"{status_emoji} **#{feedback['id']}** - {feedback['type']}\n"
                f"Von: {feedback['username']}\n"
                f"Nachricht: {truncate_text(feedback['message'], 100)}\n"
            )
        
        embed.add_field(
            name="📋 Einträge",
            value="\n".join(feedback_text) if feedback_text else "Keine Einträge",
            inline=False
        )
        
        # Statistiken
        status_counts = {}
        type_counts = {}
        
        for feedback in self.feedback_data:
            status = feedback.get('status', 'neu')
            feedback_type = feedback.get('type', 'Allgemein')
            
            status_counts[status] = status_counts.get(status, 0) + 1
            type_counts[feedback_type] = type_counts.get(feedback_type, 0) + 1
        
        stats_text = "**Status:**\n"
        for status, count in status_counts.items():
            stats_text += f"{status}: {count}\n"
        
        stats_text += "\n**Typen:**\n"
        for feedback_type, count in list(type_counts.items())[:5]:
            stats_text += f"{feedback_type}: {count}\n"
        
        embed.add_field(
            name="📊 Statistiken",
            value=stats_text,
            inline=True
        )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)
        await log_command_usage(self.bot, interaction, "feedback_list", True)
    
    # Autocomplete für Feedback-Typen
    @feedback.autocomplete('typ')
    async def feedback_type_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        """Autocomplete für Feedback-Typen"""
        types = [
            "Allgemein", "Bug Report", "Feature Request", "Verbesserung", 
            "Lob", "Kritik", "Frage", "Sonstiges"
        ]
        
        if current:
            filtered_types = [t for t in types if current.lower() in t.lower()]
        else:
            filtered_types = types
        
        return [
            app_commands.Choice(name=t, value=t)
            for t in filtered_types[:25]
        ]
    
    @feedback_list.autocomplete('status')
    async def feedback_status_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        """Autocomplete für Feedback-Status"""
        statuses = ["neu", "bearbeitet", "erledigt", "abgelehnt"]
        
        if current:
            filtered_statuses = [s for s in statuses if current.lower() in s.lower()]
        else:
            filtered_statuses = statuses
        
        return [
            app_commands.Choice(name=s.title(), value=s)
            for s in filtered_statuses
        ]


async def setup(bot: commands.Bot):
    """Setup-Funktion für das Cog"""
    await bot.add_cog(ContactCog(bot))