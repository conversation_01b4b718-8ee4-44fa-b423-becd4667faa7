# -*- coding: utf-8 -*-
"""
Fun Cog für ButterGolem

Enthält alle Spaß-Commands wie mett, zitat, lordmeme etc.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
import aiohttp
import io
from PIL import Image, ImageDraw, ImageFont
from typing import Optional

from .utils.decorators import error_handler
from .utils.helpers import (
    get_random_quote, 
    create_embed, 
    create_error_embed,
    load_json_data,
    get_data_path
)
from .utils.logging_utils import log_command_usage

logger = logging.getLogger(__name__)


class FunCog(commands.Cog, name="Fun"):
    """Spaß-Commands für ButterGolem"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("Fun Cog geladen")
    
    @app_commands.command(
        name="mett",
        description="Zeigt ein zufälliges Mett-Bild"
    )
    @error_handler
    async def mett_command(self, interaction: discord.Interaction):
        """Zeigt ein zufälliges Mett-Bild"""
        try:
            # Lade Mett-URLs aus JSON
            mett_data = load_json_data('mett.json')
            
            if not mett_data:
                await interaction.response.send_message(
                    embed=create_error_embed("Mett-Daten konnten nicht geladen werden!"),
                    ephemeral=True
                )
                return
            
            import random
            mett_url = random.choice(mett_data)
            
            embed = create_embed(
                title="🥩 Mett des Tages",
                description="Frisches Mett für alle!",
                color=discord.Color.orange()
            )
            embed.set_image(url=mett_url)
            
            await interaction.response.send_message(embed=embed)
            await log_command_usage(self.bot, interaction, "mett", True)
            
        except Exception as e:
            logger.error(f"Fehler im mett command: {e}")
            await interaction.response.send_message(
                embed=create_error_embed("Fehler beim Laden des Mett-Bildes!"),
                ephemeral=True
            )
    
    @app_commands.command(
        name="zitat",
        description="Zeigt ein zufälliges Zitat"
    )
    @error_handler
    async def zitat_command(self, interaction: discord.Interaction):
        """Zeigt ein zufälliges Zitat"""
        try:
            quote = get_random_quote()
            
            embed = create_embed(
                title="💬 Zitat des Tages",
                description=quote,
                color=discord.Color.gold()
            )
            
            await interaction.response.send_message(embed=embed)
            await log_command_usage(self.bot, interaction, "zitat", True)
            
        except Exception as e:
            logger.error(f"Fehler im zitat command: {e}")
            await interaction.response.send_message(
                embed=create_error_embed("Fehler beim Laden des Zitats!"),
                ephemeral=True
            )
    
    @app_commands.command(
        name="lordmeme",
        description="Generiert ein Meme mit dem Drachenlord"
    )
    @app_commands.describe(
        text="Der Text für das Meme (max. 100 Zeichen)"
    )
    @error_handler
    async def lordmeme_command(self, interaction: discord.Interaction, text: str):
        """Generiert ein Meme mit dem Drachenlord"""
        if len(text) > 100:
            await interaction.response.send_message(
                embed=create_error_embed("Text ist zu lang! Maximum 100 Zeichen."),
                ephemeral=True
            )
            return
        
        await interaction.response.defer()
        
        try:
            # Lade Drachenlord-Bilder aus JSON
            lord_data = load_json_data('lord_images.json')
            
            if not lord_data:
                await interaction.followup.send(
                    embed=create_error_embed("Drachenlord-Bilder konnten nicht geladen werden!"),
                    ephemeral=True
                )
                return
            
            import random
            lord_image_url = random.choice(lord_data)
            
            # Lade das Bild herunter
            async with aiohttp.ClientSession() as session:
                async with session.get(lord_image_url) as resp:
                    if resp.status != 200:
                        await interaction.followup.send(
                            embed=create_error_embed("Fehler beim Laden des Bildes!"),
                            ephemeral=True
                        )
                        return
                    
                    image_data = await resp.read()
            
            # Erstelle Meme
            meme_image = await self._create_meme(image_data, text)
            
            if not meme_image:
                await interaction.followup.send(
                    embed=create_error_embed("Fehler beim Erstellen des Memes!"),
                    ephemeral=True
                )
                return
            
            # Sende das Meme
            file = discord.File(meme_image, filename="lordmeme.png")
            
            embed = create_embed(
                title="👑 Drachenlord Meme",
                description=f"Meme mit Text: *{text}*",
                color=discord.Color.purple()
            )
            embed.set_image(url="attachment://lordmeme.png")
            
            await interaction.followup.send(embed=embed, file=file)
            await log_command_usage(self.bot, interaction, "lordmeme", True)
            
        except Exception as e:
            logger.error(f"Fehler im lordmeme command: {e}")
            await interaction.followup.send(
                embed=create_error_embed("Fehler beim Erstellen des Memes!"),
                ephemeral=True
            )
    
    async def _create_meme(self, image_data: bytes, text: str) -> Optional[io.BytesIO]:
        """Erstellt ein Meme aus Bilddaten und Text"""
        try:
            # Öffne das Bild
            image = Image.open(io.BytesIO(image_data))
            
            # Konvertiere zu RGB falls nötig
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Skaliere das Bild auf eine vernünftige Größe
            max_size = (800, 600)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Erstelle Drawing-Kontext
            draw = ImageDraw.Draw(image)
            
            # Versuche verschiedene Schriftgrößen
            font_sizes = [60, 50, 40, 30, 25, 20]
            font = None
            
            for size in font_sizes:
                try:
                    # Versuche System-Schrift zu laden
                    font = ImageFont.truetype("arial.ttf", size)
                    break
                except:
                    try:
                        font = ImageFont.truetype("Arial.ttf", size)
                        break
                    except:
                        continue
            
            # Fallback auf Default-Font
            if not font:
                font = ImageFont.load_default()
            
            # Berechne Textposition (oben zentriert)
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (image.width - text_width) // 2
            y = 20
            
            # Zeichne Text mit Outline für bessere Lesbarkeit
            outline_width = 2
            for dx in range(-outline_width, outline_width + 1):
                for dy in range(-outline_width, outline_width + 1):
                    if dx != 0 or dy != 0:
                        draw.text((x + dx, y + dy), text, font=font, fill="black")
            
            # Zeichne den Haupttext
            draw.text((x, y), text, font=font, fill="white")
            
            # Speichere das Bild in BytesIO
            output = io.BytesIO()
            image.save(output, format='PNG', quality=95)
            output.seek(0)
            
            return output
            
        except Exception as e:
            logger.error(f"Fehler beim Erstellen des Memes: {e}")
            return None
    
    @app_commands.command(
        name="würfel",
        description="Würfelt eine Zahl zwischen 1 und der angegebenen Zahl"
    )
    @app_commands.describe(
        seiten="Anzahl der Seiten des Würfels (Standard: 6)"
    )
    @error_handler
    async def dice_command(self, interaction: discord.Interaction, seiten: Optional[int] = 6):
        """Würfelt eine Zahl"""
        if seiten < 2 or seiten > 1000:
            await interaction.response.send_message(
                embed=create_error_embed("Würfel muss zwischen 2 und 1000 Seiten haben!"),
                ephemeral=True
            )
            return
        
        import random
        result = random.randint(1, seiten)
        
        embed = create_embed(
            title="🎲 Würfel",
            description=f"Du hast eine **{result}** gewürfelt!\n(1-{seiten})",
            color=discord.Color.green()
        )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "würfel", True)
    
    @app_commands.command(
        name="münze",
        description="Wirft eine Münze"
    )
    @error_handler
    async def coin_command(self, interaction: discord.Interaction):
        """Wirft eine Münze"""
        import random
        result = random.choice(["Kopf", "Zahl"])
        emoji = "🪙" if result == "Kopf" else "💰"
        
        embed = create_embed(
            title=f"{emoji} Münzwurf",
            description=f"Die Münze zeigt: **{result}**",
            color=discord.Color.gold()
        )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "münze", True)
    
    @app_commands.command(
        name="8ball",
        description="Stellt der magischen 8-Ball eine Frage"
    )
    @app_commands.describe(
        frage="Deine Frage an die magische 8-Ball"
    )
    @error_handler
    async def eightball_command(self, interaction: discord.Interaction, frage: str):
        """Magische 8-Ball"""
        if len(frage) > 200:
            await interaction.response.send_message(
                embed=create_error_embed("Frage ist zu lang! Maximum 200 Zeichen."),
                ephemeral=True
            )
            return
        
        import random
        
        antworten = [
            "Es ist sicher", "Ohne Zweifel", "Ja, definitiv", "Du kannst dich darauf verlassen",
            "Wie ich es sehe, ja", "Höchstwahrscheinlich", "Gute Aussichten", "Ja",
            "Die Zeichen deuten auf ja", "Antworte unklar, versuche es nochmal",
            "Frage später nochmal", "Besser, ich sage es dir jetzt nicht",
            "Kann ich jetzt nicht vorhersagen", "Konzentriere dich und frage nochmal",
            "Rechne nicht damit", "Meine Antwort ist nein", "Meine Quellen sagen nein",
            "Die Aussichten sind nicht so gut", "Sehr zweifelhaft", "Nein"
        ]
        
        antwort = random.choice(antworten)
        
        embed = create_embed(
            title="🎱 Magische 8-Ball",
            description=f"**Frage:** {frage}\n\n**Antwort:** {antwort}",
            color=discord.Color.dark_blue()
        )
        
        await interaction.response.send_message(embed=embed)
        await log_command_usage(self.bot, interaction, "8ball", True)


async def setup(bot: commands.Bot):
    """Setup-Funktion für das Cog"""
    await bot.add_cog(FunCog(bot))