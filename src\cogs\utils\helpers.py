# -*- coding: utf-8 -*-
"""
Hilfsfunktionen für ButterGolem Cogs

Enthält gemeinsame Funktionen die von mehreren Cogs verwendet werden.
"""

import discord
import json
import random
import os
from typing import Optional, List, Dict, Any
import logging

logger = logging.getLogger(__name__)


def get_data_path(filename: str) -> str:
    """Gibt den korrekten Pfad für JSON-Dateien zurück"""
    # Versuche verschiedene Pfade für die JSON-Dateien
    data_paths = [
        f'src/data/{filename}',
        f'data/{filename}',
        f'../data/{filename}',
        f'/app/data/{filename}'
    ]
    
    for path in data_paths:
        if os.path.exists(path):
            return path
    
    # Fallback: ersten Pfad zurückgeben
    return data_paths[0]


def load_json_data(filename: str) -> Optional[Dict[str, Any]]:
    """Lädt JSON-Daten aus einer Datei"""
    try:
        path = get_data_path(filename)
        with open(path, mode="r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"JSON-Datei nicht gefunden: {filename}")
        return None
    except json.JSONDecodeError:
        logger.error(f"Fehler beim Parsen der JSON-Datei: {filename}")
        return None
    except Exception as e:
        logger.error(f"Unerwarteter Fehler beim Laden von {filename}: {e}")
        return None


def save_json_data(filename: str, data: Dict[str, Any]) -> bool:
    """Speichert JSON-Daten in eine Datei"""
    try:
        path = get_data_path(filename)
        # Stelle sicher, dass das Verzeichnis existiert
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        with open(path, mode="w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"Fehler beim Speichern von {filename}: {e}")
        return False


def get_random_quote() -> str:
    """Lädt ein zufälliges Zitat"""
    try:
        quotes_data = load_json_data('quotes.json')
        names_data = load_json_data('names.json')
        
        if quotes_data and names_data:
            name = random.choice(names_data)
            quote = random.choice(quotes_data)
            return f"{name} sagt: {quote}"
        else:
            return "Der Drachenlord sagt: Meddl Leude!"
    except Exception as e:
        logger.error(f"Fehler beim Laden der Zitate: {e}")
        return "Der Drachenlord sagt: Meddl Leude!"


def create_embed(
    title: str = None,
    description: str = None,
    color: discord.Color = discord.Color.gold(),
    thumbnail: str = None,
    footer: str = None,
    fields: List[Dict[str, Any]] = None
) -> discord.Embed:
    """Erstellt ein standardisiertes Discord Embed"""
    embed = discord.Embed(color=color)
    
    if title:
        embed.title = title
    if description:
        embed.description = description
    if thumbnail:
        embed.set_thumbnail(url=thumbnail)
    if footer:
        embed.set_footer(text=footer)
    
    if fields:
        for field in fields:
            embed.add_field(
                name=field.get('name', ''),
                value=field.get('value', ''),
                inline=field.get('inline', True)
            )
    
    return embed


def create_error_embed(message: str) -> discord.Embed:
    """Erstellt ein Fehler-Embed"""
    return create_embed(
        title="❌ Fehler",
        description=message,
        color=discord.Color.red()
    )


def create_success_embed(message: str) -> discord.Embed:
    """Erstellt ein Erfolg-Embed"""
    return create_embed(
        title="✅ Erfolgreich",
        description=message,
        color=discord.Color.green()
    )


def create_info_embed(message: str) -> discord.Embed:
    """Erstellt ein Info-Embed"""
    return create_embed(
        title="ℹ️ Information",
        description=message,
        color=discord.Color.blue()
    )


def format_uptime(seconds: int) -> str:
    """Formatiert Uptime in lesbares Format"""
    days = seconds // 86400
    hours = (seconds % 86400) // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if days > 0:
        return f"{days}d {hours}h {minutes}m {seconds}s"
    elif hours > 0:
        return f"{hours}h {minutes}m {seconds}s"
    elif minutes > 0:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"


def truncate_text(text: str, max_length: int = 2000) -> str:
    """Kürzt Text auf maximale Länge"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def get_guild_prefix(guild_id: int) -> str:
    """Gibt den Server-spezifischen Prefix zurück (falls implementiert)"""
    # Placeholder für zukünftige Prefix-Funktionalität
    return "/"


def is_url(text: str) -> bool:
    """Prüft ob Text eine URL ist"""
    return text.startswith(('http://', 'https://', 'ftp://'))


def clean_filename(filename: str) -> str:
    """Bereinigt Dateinamen von ungültigen Zeichen"""
    import re
    # Entferne ungültige Zeichen für Dateinamen
    return re.sub(r'[<>:"/\\|?*]', '_', filename)