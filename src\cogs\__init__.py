# -*- coding: utf-8 -*-
"""
ButterGolem Cogs Package

Dieses Paket enthält alle Discord Cogs für den ButterGolem Bot.
Jede Cog-Datei ist für eine spezifische Funktionalität zuständig.
"""

import discord
from discord.ext import commands
import logging

# Logger für Cogs
logger = logging.getLogger(__name__)


async def setup_all_cogs(bot: commands.Bot):
    """Lädt alle verfügbaren Cogs"""
    cogs_to_load = [
        'cogs.admin',
        'cogs.fun', 
        'cogs.sound',
        'cogs.quiz',
        'cogs.info',
        'cogs.contact',
        'cogs.butteriq',
        'cogs.drachigotchi'
    ]
    
    loaded_cogs = []
    failed_cogs = []
    
    for cog in cogs_to_load:
        try:
            await bot.load_extension(cog)
            loaded_cogs.append(cog)
            logger.info(f"✅ Cog geladen: {cog}")
        except Exception as e:
            failed_cogs.append((cog, str(e)))
            logger.error(f"❌ <PERSON><PERSON> beim <PERSON> von {cog}: {e}")
    
    return loaded_cogs, failed_cogs


async def reload_all_cogs(bot: commands.Bot):
    """Lädt alle Cogs neu"""
    cogs_to_reload = list(bot.extensions.keys())
    reloaded_cogs = []
    failed_cogs = []
    
    for cog in cogs_to_reload:
        try:
            await bot.reload_extension(cog)
            reloaded_cogs.append(cog)
            logger.info(f"🔄 Cog neu geladen: {cog}")
        except Exception as e:
            failed_cogs.append((cog, str(e)))
            logger.error(f"❌ Fehler beim Neuladen von {cog}: {e}")
    
    return reloaded_cogs, failed_cogs


def get_available_cogs():
    """Gibt Liste aller verfügbaren Cogs zurück"""
    return [
        'admin',
        'fun',
        'sound', 
        'quiz',
        'info',
        'contact',
        'butteriq'
    ]


def get_cog_info():
    """Gibt Informationen über alle Cogs zurück"""
    return {
        'admin': 'Admin-Befehle für Moderation und Server-Management',
        'fun': 'Spaß-Befehle wie Memes, Zitate und Mett-Level',
        'sound': 'Sound-System mit 500+ Clips',
        'quiz': 'Quiz-System mit verschiedenen Kategorien',
        'info': 'Info-Befehle wie Ping, Hilfe und Bot-Status',
        'contact': 'Kontakt-System für User-Support',
        'butteriq': 'ButterIQ KI-Integration mit Memory-System'
    }