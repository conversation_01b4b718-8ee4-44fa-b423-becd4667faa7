# -*- coding: utf-8 -*-
"""
Admin Cog für ButterGolem

Enthält alle Admin-Commands wie ban, unban, leave etc.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
from typing import Optional

from .utils.decorators import admin_only, error_handler
from .utils.helpers import create_embed, create_success_embed, create_error_embed
from .utils.logging_utils import log_admin_action, log_command_usage

logger = logging.getLogger(__name__)


class AdminCog(commands.Cog, name="Admin"):
    """Admin-Commands für ButterGolem"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("Admin Cog geladen")
    
    @app_commands.command(
        name="ban",
        description="Bannt einen User vom Server"
    )
    @app_commands.describe(
        user="Der User der gebannt werden soll",
        reason="Grund für den Bann (optional)"
    )
    @admin_only()
    @error_handler
    async def ban_user(self, interaction: discord.Interaction, user: discord.Member, reason: Optional[str] = None):
        """Bannt einen User vom Server"""
        if not interaction.guild:
            await interaction.response.send_message(
                embed=create_error_embed("Dieser Command funktioniert nur auf Servern!"),
                ephemeral=True
            )
            return
        
        # Prüfe Bot-Permissions
        if not interaction.guild.me.guild_permissions.ban_members:
            await interaction.response.send_message(
                embed=create_error_embed("Bot hat keine Ban-Berechtigung!"),
                ephemeral=True
            )
            return
        
        # Prüfe ob User bannbar ist
        if user.top_role >= interaction.guild.me.top_role:
            await interaction.response.send_message(
                embed=create_error_embed("Kann diesen User nicht bannen (höhere Rolle)!"),
                ephemeral=True
            )
            return
        
        if user == interaction.user:
            await interaction.response.send_message(
                embed=create_error_embed("Du kannst dich nicht selbst bannen!"),
                ephemeral=True
            )
            return
        
        try:
            ban_reason = reason or "Kein Grund angegeben"
            await user.ban(reason=f"Gebannt von {interaction.user}: {ban_reason}")
            
            embed = create_success_embed(
                f"**{user.mention}** wurde erfolgreich gebannt!\n"
                f"**Grund:** {ban_reason}"
            )
            await interaction.response.send_message(embed=embed)
            
            # Logge Admin-Aktion
            await log_admin_action(
                self.bot,
                interaction.user,
                "User gebannt",
                f"{user.name}#{user.discriminator}",
                ban_reason
            )
            
            await log_command_usage(self.bot, interaction, "admin ban", True)
            
        except discord.Forbidden:
            await interaction.response.send_message(
                embed=create_error_embed("Keine Berechtigung zum Bannen!"),
                ephemeral=True
            )
        except Exception as e:
            await interaction.response.send_message(
                embed=create_error_embed(f"Fehler beim Bannen: {str(e)}"),
                ephemeral=True
            )
    
    @app_commands.command(
        name="unban",
        description="Entbannt einen User"
    )
    @app_commands.describe(
        user_id="Die User-ID des zu entbannenden Users"
    )
    @admin_only()
    @error_handler
    async def unban_user(self, interaction: discord.Interaction, user_id: str):
        """Entbannt einen User"""
        if not interaction.guild:
            await interaction.response.send_message(
                embed=create_error_embed("Dieser Command funktioniert nur auf Servern!"),
                ephemeral=True
            )
            return
        
        # Prüfe Bot-Permissions
        if not interaction.guild.me.guild_permissions.ban_members:
            await interaction.response.send_message(
                embed=create_error_embed("Bot hat keine Ban-Berechtigung!"),
                ephemeral=True
            )
            return
        
        try:
            user_id_int = int(user_id)
            user = await self.bot.fetch_user(user_id_int)
            
            # Prüfe ob User gebannt ist
            banned_users = [ban_entry.user async for ban_entry in interaction.guild.bans()]
            if user not in banned_users:
                await interaction.response.send_message(
                    embed=create_error_embed("Dieser User ist nicht gebannt!"),
                    ephemeral=True
                )
                return
            
            await interaction.guild.unban(user, reason=f"Entbannt von {interaction.user}")
            
            embed = create_success_embed(
                f"**{user.name}#{user.discriminator}** wurde erfolgreich entbannt!"
            )
            await interaction.response.send_message(embed=embed)
            
            # Logge Admin-Aktion
            await log_admin_action(
                self.bot,
                interaction.user,
                "User entbannt",
                f"{user.name}#{user.discriminator}"
            )
            
            await log_command_usage(self.bot, interaction, "admin unban", True)
            
        except ValueError:
            await interaction.response.send_message(
                embed=create_error_embed("Ungültige User-ID!"),
                ephemeral=True
            )
        except discord.NotFound:
            await interaction.response.send_message(
                embed=create_error_embed("User nicht gefunden!"),
                ephemeral=True
            )
        except discord.Forbidden:
            await interaction.response.send_message(
                embed=create_error_embed("Keine Berechtigung zum Entbannen!"),
                ephemeral=True
            )
        except Exception as e:
            await interaction.response.send_message(
                embed=create_error_embed(f"Fehler beim Entbannen: {str(e)}"),
                ephemeral=True
            )
    
    @app_commands.command(
        name="leave",
        description="Lässt den Bot den Server verlassen"
    )
    @app_commands.describe(
        guild_id="Die Server-ID (optional, Standard: aktueller Server)"
    )
    @admin_only()
    @error_handler
    async def leave_guild(self, interaction: discord.Interaction, guild_id: Optional[str] = None):
        """Lässt den Bot einen Server verlassen"""
        try:
            if guild_id:
                # Verlasse spezifischen Server
                target_guild_id = int(guild_id)
                target_guild = self.bot.get_guild(target_guild_id)
                
                if not target_guild:
                    await interaction.response.send_message(
                        embed=create_error_embed("Server nicht gefunden!"),
                        ephemeral=True
                    )
                    return
                
                guild_name = target_guild.name
                await target_guild.leave()
                
                embed = create_success_embed(
                    f"Bot hat den Server **{guild_name}** verlassen!"
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
            else:
                # Verlasse aktuellen Server
                if not interaction.guild:
                    await interaction.response.send_message(
                        embed=create_error_embed("Dieser Command funktioniert nur auf Servern!"),
                        ephemeral=True
                    )
                    return
                
                guild_name = interaction.guild.name
                
                await interaction.response.send_message(
                    embed=create_success_embed(f"Bot verlässt den Server **{guild_name}**!"),
                    ephemeral=True
                )
                
                await interaction.guild.leave()
            
            # Logge Admin-Aktion
            await log_admin_action(
                self.bot,
                interaction.user,
                "Server verlassen",
                guild_name
            )
            
            await log_command_usage(self.bot, interaction, "admin leave", True)
            
        except ValueError:
            await interaction.response.send_message(
                embed=create_error_embed("Ungültige Server-ID!"),
                ephemeral=True
            )
        except Exception as e:
            await interaction.response.send_message(
                embed=create_error_embed(f"Fehler beim Verlassen: {str(e)}"),
                ephemeral=True
            )
    
    @app_commands.command(
        name="reload",
        description="Lädt ein Cog neu"
    )
    @app_commands.describe(
        cog_name="Name des Cogs das neu geladen werden soll"
    )
    @admin_only()
    @error_handler
    async def reload_cog(self, interaction: discord.Interaction, cog_name: str):
        """Lädt ein Cog neu"""
        try:
            # Versuche Cog zu reloaden
            await self.bot.reload_extension(f"src.cogs.{cog_name.lower()}")
            
            embed = create_success_embed(
                f"Cog **{cog_name}** wurde erfolgreich neu geladen!"
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            
            # Logge Admin-Aktion
            await log_admin_action(
                self.bot,
                interaction.user,
                "Cog neu geladen",
                cog_name
            )
            
            await log_command_usage(self.bot, interaction, "admin reload", True)
            
        except commands.ExtensionNotLoaded:
            await interaction.response.send_message(
                embed=create_error_embed(f"Cog **{cog_name}** ist nicht geladen!"),
                ephemeral=True
            )
        except commands.ExtensionNotFound:
            await interaction.response.send_message(
                embed=create_error_embed(f"Cog **{cog_name}** nicht gefunden!"),
                ephemeral=True
            )
        except Exception as e:
            await interaction.response.send_message(
                embed=create_error_embed(f"Fehler beim Neuladen: {str(e)}"),
                ephemeral=True
            )
    
    @app_commands.command(
        name="status",
        description="Zeigt Bot-Status und Statistiken"
    )
    @admin_only()
    @error_handler
    async def bot_status(self, interaction: discord.Interaction):
        """Zeigt Bot-Status und Statistiken"""
        import psutil
        import time
        from datetime import datetime, timedelta
        
        # Bot-Statistiken
        guild_count = len(self.bot.guilds)
        user_count = sum(guild.member_count for guild in self.bot.guilds)
        
        # System-Statistiken
        process = psutil.Process()
        memory_usage = process.memory_info().rss / 1024 / 1024  # MB
        cpu_usage = process.cpu_percent()
        
        # Uptime
        uptime_seconds = int(time.time() - process.create_time())
        uptime = str(timedelta(seconds=uptime_seconds))
        
        embed = create_embed(
            title="🤖 Bot Status",
            color=discord.Color.blue(),
            fields=[
                {"name": "📊 Statistiken", "value": f"Server: {guild_count}\nUser: {user_count}", "inline": True},
                {"name": "💾 System", "value": f"RAM: {memory_usage:.1f} MB\nCPU: {cpu_usage}%", "inline": True},
                {"name": "⏱️ Uptime", "value": uptime, "inline": True},
                {"name": "🔧 Cogs", "value": f"Geladen: {len(self.bot.cogs)}", "inline": True},
                {"name": "📡 Latenz", "value": f"{round(self.bot.latency * 1000)}ms", "inline": True},
                {"name": "🐍 Python", "value": f"Discord.py {discord.__version__}", "inline": True}
            ]
        )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)
        await log_command_usage(self.bot, interaction, "admin status", True)


async def setup(bot: commands.Bot):
    """Setup-Funktion für das Cog"""
    await bot.add_cog(AdminCog(bot))