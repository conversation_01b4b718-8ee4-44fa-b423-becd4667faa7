#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB Migration Script für Buttergolem Discord Bot
Migriert alle lokalen JSON-Daten zu MongoDB
"""

import os
import json
import pymongo
from datetime import datetime
from pymongo import MongoClient
from typing import Dict, List, Any
import logging

# MongoDB Verbindung
MONGODB_URI = "mongodb+srv://nindscher:<EMAIL>/?retryWrites=true&w=majority&appName=buttergolem1"
DATABASE_NAME = "buttergolem1"

# Logging konfigurieren
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MongoDBMigrator:
    def __init__(self, uri: str, db_name: str):
        """Initialisiert die MongoDB Verbindung"""
        try:
            self.client = MongoClient(uri)
            self.db = self.client[db_name]
            logger.info(f"✅ Verbunden mit MongoDB Datenbank: {db_name}")
        except Exception as e:
            logger.error(f"❌ MongoDB Verbindung fehlgeschlagen: {e}")
            raise

    def create_collections(self):
        """Erstellt alle notwendigen Collections"""
        collections = [
            'users',              # Benutzerdaten
            'servers',            # Server-Informationen
            'drachigotchis',      # Drachigotchi-Spielstände
            'ai_chat_history',    # KI-Chat Verlauf
            'drache_ki_data',     # Drachenlord-KI-Daten
            'butteriq_sessions',  # ButterIQ Sessions
            'butteriq_users',     # ButterIQ Benutzerdaten
            'memories',           # User Memories
            'statistics',         # Bot-Statistiken
            'sound_plays',        # Sound-Wiedergaben
            'quiz_results',       # Quiz-Ergebnisse
            'donations',          # Spenden-Logs
            'bans',               # Server/User Bans
            'commands',           # Command-Usage
            'achievements'        # User Achievements
        ]
        
        for collection_name in collections:
            if collection_name not in self.db.list_collection_names():
                self.db.create_collection(collection_name)
                logger.info(f"📁 Collection erstellt: {collection_name}")

    def migrate_drachigotchis(self):
        """Migriert Drachigotchi-Daten"""
        file_path = "data/drachigotchis.json"
        if not os.path.exists(file_path):
            logger.warning(f"⚠️ Datei nicht gefunden: {file_path}")
            return

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            collection = self.db['drachigotchis']
            migrated_count = 0
            
            for user_id_str, drachi_data in data.items():
                try:
                    user_id = int(user_id_str)
                    document = {
                        'user_id': user_id,
                        'name': drachi_data.get('name', 'Unbekannt'),
                        'level': drachi_data.get('level', 1),
                        'exp': drachi_data.get('exp', 0),
                        'health': drachi_data.get('health', 100),
                        'hunger': drachi_data.get('hunger', 100),
                        'happiness': drachi_data.get('happiness', 100),
                        'energy': drachi_data.get('energy', 100),
                        'money': drachi_data.get('money', 50),
                        'fame': drachi_data.get('fame', 0),
                        'location': drachi_data.get('location', 'schanze'),
                        'inventory': drachi_data.get('inventory', {}),
                        'equipment': drachi_data.get('equipment', {}),
                        'skills': drachi_data.get('skills', {}),
                        'achievements': drachi_data.get('achievements', []),
                        'created_at': datetime.fromisoformat(drachi_data.get('created_at', datetime.now().isoformat())),
                        'last_update': datetime.now()
                    }
                    
                    collection.replace_one(
                        {'user_id': user_id},
                        document,
                        upsert=True
                    )
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Fehler bei User {user_id_str}: {e}")
            
            logger.info(f"🐉 {migrated_count} Drachigotchis migriert")
            
        except Exception as e:
            logger.error(f"❌ Fehler beim Migrieren der Drachigotchis: {e}")

    def migrate_memories(self):
        """Migriert User Memories"""
        memories_dir = "data/memories"
        if not os.path.exists(memories_dir):
            logger.warning(f"⚠️ Verzeichnis nicht gefunden: {memories_dir}")
            return

        try:
            collection = self.db['memories']
            migrated_count = 0
            
            for filename in os.listdir(memories_dir):
                if filename.endswith('.json'):
                    user_id = int(filename.replace('.json', ''))
                    file_path = os.path.join(memories_dir, filename)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            memory_data = json.load(f)
                        
                        document = {
                            'user_id': user_id,
                            'user_info': memory_data.get('user_info', {}),
                            'important_facts': memory_data.get('important_facts', []),
                            'conversation_history': memory_data.get('conversation_history', []),
                            'created_at': datetime.now(),
                            'last_updated': datetime.now()
                        }
                        
                        collection.replace_one(
                            {'user_id': user_id},
                            document,
                            upsert=True
                        )
                        migrated_count += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Fehler bei Memory {user_id}: {e}")
            
            logger.info(f"🧠 {migrated_count} Memories migriert")
            
        except Exception as e:
            logger.error(f"❌ Fehler beim Migrieren der Memories: {e}")

    def migrate_statistics(self):
        """Migriert Bot-Statistiken"""
        stats_files = [
            "data/stats.json",
            "src/ai_chatbot/logs/stats.json"
        ]
        
        collection = self.db['statistics']
        migrated_count = 0
        
        for file_path in stats_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        stats_data = json.load(f)
                    
                    document = {
                        'source': os.path.basename(file_path),
                        'data': stats_data,
                        'migrated_at': datetime.now()
                    }
                    
                    collection.insert_one(document)
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Fehler bei Stats {file_path}: {e}")
        
        logger.info(f"📊 {migrated_count} Statistik-Dateien migriert")

    def migrate_ai_data(self):
        """Migriert AI-Chatbot und Drachenlord-Daten"""
        # Basis AI-Dateien aus data/
        base_ai_files = [
            "data/ai_chatbot.json",
            "data/drache.json",
            "data/drache_lore.json",
            "data/drache_events_2024_2025.json",
            "data/quotes.json",
            "data/names.json",
            "data/questions.json",
            "data/cringe.json"
        ]
        
        # Alle Drachenlord-KI-Dateien aus src/ki/
        drache_files = [
            "src/ki/drache.json",
            "src/ki/drache_copy.json",
            "src/ki/drache_adjectives.json",
            "src/ki/drache_bio.json",
            "src/ki/drache_config.json",
            "src/ki/drache_controversies_2024.json",
            "src/ki/drache_events_2024_2025.json",
            "src/ki/drache_examples.json",
            "src/ki/drache_future_plans_2025.json",
            "src/ki/drache_knowledge.json",
            "src/ki/drache_lore.json",
            "src/ki/drache_messages.json",
            "src/ki/drache_opinions_2024.json",
            "src/ki/drache_people.json",
            "src/ki/drache_phrases_2024.json",
            "src/ki/drache_reactions_2024.json",
            "src/ki/drache_style.json",
            "src/ki/drache_system_prompt.json",
            "src/ki/drache_topics.json",
            "src/ki/drache_zitate_2024.json"
        ]
        
        collection = self.db['ai_chat_history']
        migrated_count = 0
        
        # Basis AI-Dateien migrieren
        for file_path in base_ai_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        ai_data = json.load(f)
                    
                    document = {
                        'type': os.path.basename(file_path).replace('.json', ''),
                        'data': ai_data,
                        'migrated_at': datetime.now()
                    }
                    
                    collection.insert_one(document)
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Fehler bei AI-Daten {file_path}: {e}")
        
        # Drachenlord-KI-Dateien migrieren
        drache_collection = self.db['drache_ki_data']
        for file_path in drache_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        drache_data = json.load(f)
                    
                    document = {
                        'type': os.path.basename(file_path).replace('.json', ''),
                        'data': drache_data,
                        'source_path': file_path,
                        'migrated_at': datetime.now()
                    }
                    
                    drache_collection.insert_one(document)
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Fehler bei Drache-KI {file_path}: {e}")
        
        # Migriere ButterIQ Sessions
        sessions_collection = self.db['butteriq_sessions']
        logs_dir = "src/ai_chatbot/logs"
        if os.path.exists(logs_dir):
            session_file = os.path.join(logs_dir, "sessions.json")
            if os.path.exists(session_file):
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        sessions_data = json.load(f)
                    
                    sessions_collection.insert_one({
                        'type': 'butteriq_sessions',
                        'data': sessions_data,
                        'migrated_at': datetime.now()
                    })
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Fehler bei ButterIQ Sessions: {e}")
        
        # Migriere individuelle ButterIQ Benutzerdaten
        butteriq_collection = self.db['butteriq_users']
        butteriq_dir = "src/ai_chatbot"
        if os.path.exists(butteriq_dir):
            for filename in os.listdir(butteriq_dir):
                if filename.endswith('.json') and filename[:-5].isdigit():
                    file_path = os.path.join(butteriq_dir, filename)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            user_data = json.load(f)
                        
                        user_id = int(filename.replace('.json', ''))
                        butteriq_collection.insert_one({
                            'user_id': user_id,
                            'data': user_data,
                            'migrated_at': datetime.now()
                        })
                        migrated_count += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Fehler bei ButterIQ User {filename}: {e}")
        
        logger.info(f"🤖 {migrated_count} AI-Daten-Dateien migriert")

    def migrate_bans(self):
        """Migriert Ban-Daten"""
        ban_files = [
            "data/ban.json",
            "data/bans.json",
            "data/disabled_users.json"
        ]
        
        collection = self.db['bans']
        migrated_count = 0
        
        for file_path in ban_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        ban_data = json.load(f)
                    
                    document = {
                        'source': os.path.basename(file_path),
                        'bans': ban_data,
                        'migrated_at': datetime.now()
                    }
                    
                    collection.insert_one(document)
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Fehler bei Bans {file_path}: {e}")
        
        logger.info(f"🚫 {migrated_count} Ban-Daten migriert")

    def migrate_server_data(self):
        """Migriert Server-spezifische Daten aus verschiedenen Quellen"""
        # Server-IDs aus verschiedenen Dateien sammeln
        server_data = []
        
        # Aus Drachigotchis extrahieren
        drachi_file = "data/drachigotchis.json"
        if os.path.exists(drachi_file):
            try:
                with open(drachi_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                # Hier könnten Server-IDs extrahiert werden
            except:
                pass
        
        collection = self.db['servers']
        if server_data:
            collection.insert_many(server_data)
            logger.info(f"🖥️ Server-Daten migriert")

    def create_indexes(self):
        """Erstellt wichtige Indizes für Performance"""
        indexes = [
            ('users', [('user_id', 1)]),
            ('drachigotchis', [('user_id', 1)]),
            ('memories', [('user_id', 1)]),
            ('butteriq_users', [('user_id', 1)]),
            ('statistics', [('source', 1)]),
            ('bans', [('source', 1)])
        ]
        
        for collection_name, index_fields in indexes:
            try:
                self.db[collection_name].create_index(index_fields, unique=True)
                logger.info(f"🔍 Index erstellt für {collection_name}")
            except Exception as e:
                logger.warning(f"⚠️ Index-Fehler für {collection_name}: {e}")

    def run_migration(self):
        """Führt die vollständige Migration durch"""
        logger.info("🚀 Starte MongoDB Migration...")
        
        try:
            # Collections erstellen
            self.create_collections()
            
            # Daten migrieren
            self.migrate_drachigotchis()
            self.migrate_memories()
            self.migrate_statistics()
            self.migrate_ai_data()
            self.migrate_bans()
            self.migrate_server_data()
            
            # Indizes erstellen
            self.create_indexes()
            
            logger.info("✅ Migration erfolgreich abgeschlossen!")
            
        except Exception as e:
            logger.error(f"❌ Migration fehlgeschlagen: {e}")
            raise

    def verify_migration(self):
        """Überprüft die erfolgreiche Migration"""
        stats = {}
        for collection_name in self.db.list_collection_names():
            count = self.db[collection_name].count_documents({})
            stats[collection_name] = count
            
        logger.info("📊 Migration-Statistik:")
        for collection, count in stats.items():
            logger.info(f"   {collection}: {count} Dokumente")
        
        return stats

    def close(self):
        """Schließt die MongoDB Verbindung"""
        self.client.close()
        logger.info("🔒 MongoDB Verbindung geschlossen")

def main():
    """Hauptfunktion für die Migration"""
    migrator = None
    try:
        migrator = MongoDBMigrator(MONGODB_URI, DATABASE_NAME)
        migrator.run_migration()
        stats = migrator.verify_migration()
        
        print("\n" + "="*50)
        print("🎉 MIGRATION ABGESCHLOSSEN 🎉")
        print("="*50)
        for collection, count in stats.items():
            print(f"📁 {collection}: {count} Dokumente")
        print("\n✅ Alle Daten wurden erfolgreich zu MongoDB migriert!")
        
    except Exception as e:
        print(f"\n❌ Fehler: {e}")
        return 1
    finally:
        if migrator:
            migrator.close()
    
    return 0

if __name__ == "__main__":
    exit(main())