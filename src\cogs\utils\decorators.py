# -*- coding: utf-8 -*-
"""
Decorators für ButterGolem Cogs

Enthält Admin-Checks, Error-Handling und andere Decorators.
"""

import discord
from discord import app_commands
from discord.ext import commands
import os
import functools
import logging

logger = logging.getLogger(__name__)

# Admin-Konfiguration aus Environment Variables
ADMIN_USER_ID = int(os.getenv('ADMIN_USER_ID', 0))


def admin_only():
    """Decorator der Commands nur für Admins sichtbar macht"""
    def predicate(interaction: discord.Interaction) -> bool:
        return interaction.user.id == ADMIN_USER_ID
    return app_commands.check(predicate)


def error_handler(func):
    """Decorator für einheitliches Error-Handling"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except discord.errors.NotFound:
            logger.error(f"Discord NotFound Error in {func.__name__}")
            # Interaction bereits beantwortet oder nicht mehr gültig
            pass
        except discord.errors.Forbidden:
            logger.error(f"Discord Forbidden Error in {func.__name__}")
            # Bot hat keine Berechtigung
            pass
        except Exception as e:
            logger.error(f"Unerwarteter Fehler in {func.__name__}: {e}")
            # Versuche Fehler an User zu senden
            if len(args) > 0 and hasattr(args[0], 'response'):
                interaction = args[0]
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            f"❌ Ein Fehler ist aufgetreten: {str(e)}", 
                            ephemeral=True
                        )
                    else:
                        await interaction.followup.send(
                            f"❌ Ein Fehler ist aufgetreten: {str(e)}", 
                            ephemeral=True
                        )
                except:
                    pass  # Fehler beim Senden der Fehlermeldung ignorieren
    return wrapper


def rate_limit(rate: int = 1, per: float = 60.0):
    """Rate-Limiting Decorator (falls benötigt)"""
    def decorator(func):
        # Einfache Rate-Limiting Implementation
        # Kann später erweitert werden
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_voice_channel(func):
    """Decorator der prüft, ob User in einem Voice Channel ist"""
    @functools.wraps(func)
    async def wrapper(interaction: discord.Interaction, *args, **kwargs):
        if not interaction.user.voice or not interaction.user.voice.channel:
            await interaction.response.send_message(
                "❌ Du musst in einem Voice Channel sein!", 
                ephemeral=True
            )
            return
        return await func(interaction, *args, **kwargs)
    return wrapper


def require_bot_permissions(*permissions):
    """Decorator der prüft, ob Bot bestimmte Permissions hat"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(interaction: discord.Interaction, *args, **kwargs):
            if not interaction.guild:
                await interaction.response.send_message(
                    "❌ Dieser Command funktioniert nur auf Servern!", 
                    ephemeral=True
                )
                return
            
            bot_permissions = interaction.guild.me.guild_permissions
            missing_permissions = []
            
            for permission in permissions:
                if not getattr(bot_permissions, permission, False):
                    missing_permissions.append(permission)
            
            if missing_permissions:
                await interaction.response.send_message(
                    f"❌ Bot benötigt folgende Permissions: {', '.join(missing_permissions)}", 
                    ephemeral=True
                )
                return
            
            return await func(interaction, *args, **kwargs)
        return wrapper
    return decorator