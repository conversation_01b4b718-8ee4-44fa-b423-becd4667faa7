#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB Migration Verification Script
Überprüft die erfolgreiche Migration aller Daten
"""

import os
import json
from pymongo import MongoClient
from datetime import datetime
import logging

# MongoDB Verbindung
MONGODB_URI = "mongodb+srv://nindscher:<EMAIL>/?retryWrites=true&w=majority&appName=buttergolem1"
DATABASE_NAME = "buttergolem1"

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MigrationVerifier:
    def __init__(self, uri: str, db_name: str):
        try:
            self.client = MongoClient(uri)
            self.db = self.client[db_name]
            logger.info(f"✅ Verbunden mit MongoDB Datenbank: {db_name}")
        except Exception as e:
            logger.error(f"❌ MongoDB Verbindung fehlgeschlagen: {e}")
            raise

    def get_collection_stats(self):
        """Zeigt Statistiken aller Collections"""
        collections = self.db.list_collection_names()
        
        print("\n" + "="*60)
        print("📊 MONGODB MIGRATION STATUS")
        print("="*60)
        
        if not collections:
            print("❌ Keine Collections gefunden!")
            return

        for collection_name in sorted(collections):
            count = self.db[collection_name].count_documents({})
            print(f"📁 {collection_name:<20} | {count:>6} Dokumente")
            
            # Zeige Beispiel-Dokumente
            if count > 0:
                sample = self.db[collection_name].find_one()
                if sample:
                    keys = list(sample.keys())[:5]  # Erste 5 Keys
                    print(f"   🔍 Keys: {', '.join(keys)}")
                    if '_id' in sample:
                        print(f"   🆔 Sample ID: {str(sample['_id'])[:8]}...")
            print()

    def verify_drachigotchis(self):
        """Überprüft die Drachigotchi-Daten"""
        collection = self.db['drachigotchis']
        count = collection.count_documents({})
        
        print("🐉 DRACHIGOTCHI VERIFIKATION:")
        print(f"   Gesamt: {count} Drachigotchis")
        
        if count > 0:
            # Zeige erste 3 Drachigotchis
            for doc in collection.find().limit(3):
                print(f"   👤 User {doc['user_id']}: {doc['name']} (Level {doc['level']})")
        print()

    def verify_memories(self):
        """Überprüft die Memory-Daten"""
        collection = self.db['memories']
        count = collection.count_documents({})
        
        print("🧠 MEMORY VERIFIKATION:")
        print(f"   Gesamt: {count} Memories")
        
        if count > 0:
            # Zeige erste 3 Memories
            for doc in collection.find().limit(3):
                user_info = doc.get('user_info', {})
                username = user_info.get('username', 'Unbekannt')
                print(f"   👤 User {doc['user_id']}: {username}")
        print()

    def verify_ai_data(self):
        """Überprüft die AI-Daten"""
        collection = self.db['ai_chat_history']
        count = collection.count_documents({})
        
        expected_types = ['ai_chatbot', 'drache', 'drache_lore', 'drache_events_2024_2025', 'quotes', 'names', 'questions', 'cringe']
        found_types = []
        
        for doc in collection.find({}, {'type': 1}):
            if 'type' in doc:
                found_types.append(doc['type'])
        
        missing = set(expected_types) - set(found_types)
        
        # Überprüfe Drachenlord-KI-Daten
        drache_collection = self.db['drache_ki_data']
        drache_count = drache_collection.count_documents({})
        
        expected_drache_types = [
            'drache', 'drache_copy', 'drache_adjectives', 'drache_bio', 'drache_config',
            'drache_controversies_2024', 'drache_events_2024_2025', 'drache_examples',
            'drache_future_plans_2025', 'drache_knowledge', 'drache_lore', 'drache_messages',
            'drache_opinions_2024', 'drache_people', 'drache_phrases_2024',
            'drache_reactions_2024', 'drache_style', 'drache_system_prompt',
            'drache_topics', 'drache_zitate_2024'
        ]
        
        found_drache_types = []
        for doc in drache_collection.find({}, {'type': 1}):
            if 'type' in doc:
                found_drache_types.append(doc['type'])
        
        missing_drache = set(expected_drache_types) - set(found_drache_types)
        
        # Überprüfe ButterIQ Sessions
        sessions_collection = self.db['butteriq_sessions']
        sessions_count = sessions_collection.count_documents({})
        
        # Überprüfe ButterIQ Benutzerdaten
        butteriq_collection = self.db['butteriq_users']
        butteriq_count = butteriq_collection.count_documents({})
        
        print("🤖 AI-DATEN VERIFIKATION:")
        print(f"   Gesamt: {count} AI-Datensätze")
        
        if count > 0:
            # Zeige alle AI-Daten-Typen
            types = collection.distinct('type')
            for data_type in types:
                type_count = collection.count_documents({'type': data_type})
                print(f"   📄 {data_type}: {type_count} Einträge")
        
        print(f"   📊 Drachenlord-KI: {drache_count} Dokumente")
        print(f"   📊 ButterIQ Sessions: {sessions_count} Dokumente")
        print(f"   📊 ButterIQ Benutzer: {butteriq_count} Dokumente")
        if missing:
            print(f"   ⚠️ Fehlende Basis-Typen: {', '.join(missing)}")
        if missing_drache:
            print(f"   ⚠️ Fehlende Drache-Typen: {', '.join(missing_drache)}")
        print()

    def compare_with_json_files(self):
        """Vergleicht mit vorhandenen JSON-Dateien"""
        json_files = [
            ("data/drachigotchis.json", "drachigotchis"),
            ("data/ai_chatbot.json", "ai_chat_history"),
            ("data/ban.json", "bans"),
            ("data/stats.json", "statistics")
        ]
        
        print("📋 JSON vs MONGODB VERGLEICH:")
        
        for file_path, collection_name in json_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    
                    json_count = len(json_data) if isinstance(json_data, dict) else len(json_data)
                    mongo_count = self.db[collection_name].count_documents({})
                    
                    status = "✅" if json_count <= mongo_count else "⚠️"
                    print(f"   {status} {file_path:<25} | JSON: {json_count:>4} | MongoDB: {mongo_count:>4}")
                    
                except Exception as e:
                    print(f"   ❌ Fehler bei {file_path}: {e}")
            else:
                print(f"   ⚠️ {file_path} nicht gefunden")
        print()

    def check_indexes(self):
        """Überprüft die erstellten Indizes"""
        print("🔍 INDEX VERIFIKATION:")
        
        collections = ['users', 'drachigotchis', 'memories']
        
        for collection_name in collections:
            try:
                indexes = list(self.db[collection_name].list_indexes())
                index_names = [idx['name'] for idx in indexes]
                print(f"   📁 {collection_name}: {', '.join(index_names)}")
            except Exception as e:
                print(f"   ❌ Fehler bei {collection_name}: {e}")
        print()

    def run_full_verification(self):
        """Führt die vollständige Verifizierung durch"""
        try:
            self.get_collection_stats()
            self.verify_drachigotchis()
            self.verify_memories()
            self.verify_ai_data()
            self.compare_with_json_files()
            self.check_indexes()
            
            print("✅ Verifizierung abgeschlossen!")
            
        except Exception as e:
            logger.error(f"❌ Verifizierung fehlgeschlagen: {e}")

    def close(self):
        """Schließt die Verbindung"""
        self.client.close()
        logger.info("🔒 MongoDB Verbindung geschlossen")

def main():
    """Hauptfunktion"""
    verifier = None
    try:
        verifier = MigrationVerifier(MONGODB_URI, DATABASE_NAME)
        verifier.run_full_verification()
        
    except Exception as e:
        print(f"❌ Fehler: {e}")
        return 1
    finally:
        if verifier:
            verifier.close()
    
    return 0

if __name__ == "__main__":
    exit(main())