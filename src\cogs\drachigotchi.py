# -*- coding: utf-8 -*-
import discord
from discord import app_commands
from discord.ext import commands, tasks
import json
import os
import random
import asyncio
from datetime import datetime, timedelta

# --- CONFIGURATION ---
DATA_FILE = 'data/drachigotchis.json'
LOOP_TIME_MINUTES = 20

# --- GAME DATA STRUCTURES ---

# ASCII Art & Emojis
DRACHIGOTCHI_ART = {
    'normal': "🐉 \"Etzala ein neuer Tag.\"",
    'happy': "🎉 \"Meddl on! Besser als wie man denkt!\"",
    'sad': "😢 \"Die Hater machen mich feddich.\"",
    'angry': "💢 \"Jetzt hab ich die Schnauze voll!\"",
    'hungry': "🍕 \"Ich könnt' etzala was verdrücken.\"",
    'dirty': "🛁 \"Zeit für ein Bad in der Tschechei.\"",
    'sleeping': "😴 \"Träume vom Schanzenfest...\"",
    'dead': "💀 \"Game Over, du Lellek.\"",
    'working': "💼 \"Arbeit, Arbeit... ABEID!\"",
    'rich': "🤑 \"Geld regiert die Welt!\"",
    'training': "💪 \"Stark wie ein Drache!\"",
    'defending': "🛡️ \"Die Schanze hält!\"",
    'exploring': "🗺️ \"Mal gucken, was draußen so los ist.\"",
    'questing': "📜 \"Ein Auftrag ist ein Auftrag!\"",
    'crafting': "🛠️ \"Da wird was zammegebrügelt.\""
}

# Items, Resources, and Shop
ITEMS = {
    # Food
    'mettbrötchen': {'type': 'food', 'price': 5, 'hunger': 20, 'happiness': 5, 'description': 'Ein ordentliches Mettbrötchen.'},
    'tiefkühlpizza': {'type': 'food', 'price': 8, 'hunger': 35, 'happiness': 10, 'description': 'Für den großen Hunger.'},
    'energy_drink': {'type': 'food', 'price': 3, 'hunger': 5, 'happiness': 10, 'description': 'Gibt einen schnellen Schub.'},
    # Tools & Gear
    'meddl_hammer': {'type': 'gear', 'price': 50, 'stats': {'strength': 2}, 'description': 'Ein mächtiger Hammer.'},
    'anti_hater_spray': {'type': 'consumable', 'price': 25, 'effect': {'health': 20}, 'description': 'Schützt vor fiesen Kommentaren.'},
    'buttergolem': {'type': 'special', 'price': 1000, 'happiness': 100, 'description': 'Ein treuer Freund und Helfer.'},
    # Resources
    'holz': {'type': 'resource', 'price': 10, 'description': 'Einfaches Holz, zum Bauen und Basteln.'},
    'stein': {'type': 'resource', 'price': 15, 'description': 'Stabiler Stein.'},
    'eisen': {'type': 'resource', 'price': 50, 'description': 'Wertvolles Eisenerz.'},
    # Crafted
    'holzschild': {'type': 'gear', 'stats': {'defense': 5}, 'description': 'Ein einfacher Schild aus Holz.'},
    'verbesserte_axt': {'type': 'gear', 'stats': {'strength': 3}, 'description': 'Zum besseren Holzfällen.'}
}

# Crafting Recipes
CRAFTING_RECIPES = {
    'holzschild': {'name': 'Holzschild', 'materials': {'holz': 10, 'stein': 2}, 'skill_req': {'crafting': 5}},
    'verbesserte_axt': {'name': 'Verbesserte Axt', 'materials': {'holz': 5, 'eisen': 3}, 'skill_req': {'crafting': 10}}
}

# Locations
LOCATIONS = {
    'zuhause': {'name': 'Zuhause', 'description': 'Die heilige Schanze.', 'actions': ['schlafen', 'craften']},
    'dorf': {'name': 'Dorf', 'description': 'Das nächste Dorf mit Laden und Taverne.', 'actions': ['shop', 'reden', 'quests_annehmen']},
    'wald': {'name': 'Wald', 'description': 'Ein dunkler Wald, gut für die Holzsuche.', 'actions': ['sammeln_holz']},
    'mine': {'name': 'Mine', 'description': 'Eine verlassene Mine, reich an Erzen.', 'actions': ['abbauen_stein', 'abbauen_eisen']},
    'haider-lager': {'name': 'Haider-Lager', 'description': 'Ein gefährliches Lager voller Haider.', 'actions': ['angreifen']}
}

# NPCs
NPCS = {
    'postbote': {'name': 'Postbote', 'dialogue': ["Ich hab ein Paket für dich!", "Unterschreiben Sie hier."]},
    'wirt': {'name': 'Wirt', 'dialogue': ["Was darfs sein?", "Hör mal, ich hab da was für dich..."]},
    'schmied': {'name': 'Schmied', 'dialogue': ["Heißes Eisen, kalter Stahl!", "Brauchst du was Ordentliches?"]}
}

# Quests
QUESTS = {
    'mett_quest': {'name': 'Die Mett-Mission', 'description': 'Der Wirt braucht 5 Mettbrötchen.', 'requirement': {'item': 'mettbrötchen', 'amount': 5}, 'reward': {'exp': 150, 'money': 50}},
    'holz_quest': {'name': 'Schanzen-Vorbereitung', 'description': 'Sammle 20 Holz für den Schmied.', 'requirement': {'item': 'holz', 'amount': 20}, 'reward': {'exp': 200, 'item': 'verbesserte_axt'}}
}

# Jobs
JOBS = {
    'holzfäller': {'name': 'Holzfäller', 'description': 'Du fällst Holz im Wald.', 'income': (10, 30), 'skill': 'strength'},
    'wache': {'name': 'Wache', 'description': 'Du bewachst das Dorf.', 'income': (20, 40), 'skill': 'defense'},
    'streamer': {'name': 'Streamer', 'description': 'Du unterhältst die Massen.', 'income': (5, 60), 'skill': 'charisma'}
}

# --- CORE CLASSES ---

class Drachigotchi:
    def __init__(self, user_id, name):
        self.user_id = user_id
        self.name = name
        self.age = 0
        self.level = 1
        self.exp = 0
        self.health = 100
        self.hunger = 100
        self.happiness = 100
        self.money = 50
        self.inventory = {}
        self.equipment = {'weapon': None, 'shield': None}
        self.schanze_level = 1
        self.location = 'zuhause'
        self.active_quest = None
        self.job = None
        self.skills = {'strength': 1, 'defense': 1, 'charisma': 1, 'crafting': 1, 'gathering': 1}
        self.last_update = datetime.now()
        self.created_at = datetime.now()
        self.is_sleeping = False
        self.status = 'normal'

    def to_dict(self):
        data = self.__dict__.copy()
        data['last_update'] = data['last_update'].isoformat()
        data['created_at'] = data['created_at'].isoformat()
        return data

    @classmethod
    def from_dict(cls, data):
        instance = cls(data['user_id'], data['name'])
        for key, value in data.items():
            if key in ['last_update', 'created_at'] and value:
                setattr(instance, key, datetime.fromisoformat(value))
            else:
                setattr(instance, key, value)
        return instance

    def gain_exp(self, amount):
        self.exp += amount
        leveled_up = False
        while self.exp >= self.level * 100:
            self.exp -= self.level * 100
            self.level += 1
            # Distribute skill points on level up
            self.health = self.get_max_health()
            leveled_up = True
        return leveled_up

    def get_max_health(self):
        return 100 + (self.level * 10)

    def get_total_stat(self, stat):
        base_stat = self.skills.get(stat, 0)
        for item_name in self.equipment.values():
            if item_name and item_name in ITEMS:
                base_stat += ITEMS[item_name]['stats'].get(stat, 0)
        return base_stat

class DrachigotchiManager:
    def __init__(self, file_path):
        self.file_path = file_path
        os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
        self.drachigotchis = self.load()

    def load(self):
        if not os.path.exists(self.file_path):
            return {}
        try:
            with open(self.file_path, 'r') as f:
                data = json.load(f)
                return {int(k): Drachigotchi.from_dict(v) for k, v in data.items()}
        except (json.JSONDecodeError, IOError):
            return {}

    def save(self):
        with open(self.file_path, 'w') as f:
            json.dump({k: v.to_dict() for k, v in self.drachigotchis.items()}, f, indent=4)

    def get_drachigotchi(self, user_id):
        return self.drachigotchis.get(user_id)

    def create_drachigotchi(self, user_id, name):
        if user_id in self.drachigotchis:
            return None
        drachigotchi = Drachigotchi(user_id, name)
        self.drachigotchis[user_id] = drachigotchi
        self.save()
        return drachigotchi

# --- DISCORD COG --- #

class DrachigotchiCog(commands.Cog, name="Drachigotchi"):
    """Dein ultimatives Drachenlord-Abenteuer!"""
    def __init__(self, bot):
        self.bot = bot
        self.manager = DrachigotchiManager(DATA_FILE)
        self.update_loop.start()

    gotchi = app_commands.Group(name="gotchi", description="Dein persönliches Drachigotchi-Abenteuer!")

    # --- Core Commands ---
    @gotchi.command(name="start", description="Erstelle dein Drachigotchi.")
    async def start(self, interaction: discord.Interaction, name: str):
        if self.manager.get_drachigotchi(interaction.user.id):
            return await interaction.response.send_message("Du hast bereits ein Drachigotchi!", ephemeral=True)
        self.manager.create_drachigotchi(interaction.user.id, name)
        await interaction.response.send_message(f"🎉 Willkommen, {name}! Dein Abenteuer beginnt! Nutze `/gotchi hilfe`.")

    @gotchi.command(name="status", description="Zeigt den Status deines Drachigotchis.")
    async def status(self, interaction: discord.Interaction):
        drachi = self.manager.get_drachigotchi(interaction.user.id)
        if not drachi: return await interaction.response.send_message("Starte erst mit `/gotchi start`.", ephemeral=True)
        
        embed = discord.Embed(title=f"📊 Status für {drachi.name}", color=discord.Color.gold())
        embed.add_field(name="Status", value=DRACHIGOTCHI_ART.get(drachi.status, '...'), inline=False)
        embed.add_field(name="Level", value=f"Lvl. {drachi.level} ({drachi.exp}/{drachi.level*100} EXP)", inline=True)
        embed.add_field(name="Geld", value=f"💰 {drachi.money}€", inline=True)
        embed.add_field(name="Ort", value=f"📍 {LOCATIONS[drachi.location]['name']}", inline=True)
        embed.add_field(name="Gesundheit", value=f"❤️ {drachi.health}/{drachi.get_max_health()}", inline=True)
        embed.add_field(name="Hunger", value=f"🍕 {drachi.hunger}/100", inline=True)
        embed.add_field(name="Zufriedenheit", value=f"😊 {drachi.happiness}/100", inline=True)
        
        skills_text = ', '.join([f"{s.title()}: {v}" for s, v in drachi.skills.items()])
        embed.add_field(name="Skills", value=skills_text, inline=False)
        
        await interaction.response.send_message(embed=embed)

    # --- Action Commands ---
    @gotchi.command(name="reisen", description="Reise an einen neuen Ort.")
    async def reisen(self, interaction: discord.Interaction, ort: str):
        drachi = self.manager.get_drachigotchi(interaction.user.id)
        if not drachi or ort.lower() not in LOCATIONS:
            return await interaction.response.send_message("Diesen Ort gibt es nicht.", ephemeral=True)
        drachi.location = ort.lower()
        self.manager.save()
        await interaction.response.send_message(f"Du reist nach {LOCATIONS[drachi.location]['name']}.")

    @gotchi.command(name="erkunden", description="Führe eine Aktion am aktuellen Ort aus.")
    async def erkunden(self, interaction: discord.Interaction):
        drachi = self.manager.get_drachigotchi(interaction.user.id)
        if not drachi: return await interaction.response.send_message("Starte erst mit `/gotchi start`.", ephemeral=True)
        # ... More complex exploration logic based on location and skills ...
        await interaction.response.send_message("Du schaust dich um...")

    @gotchi.command(name="craft", description="Stelle neue Gegenstände her.")
    async def craft(self, interaction: discord.Interaction, gegenstand: str):
        drachi = self.manager.get_drachigotchi(interaction.user.id)
        if not drachi or gegenstand not in CRAFTING_RECIPES:
            return await interaction.response.send_message("Dieses Rezept kennst du nicht.", ephemeral=True)

        rezept = CRAFTING_RECIPES[gegenstand]
        # Check materials
        for material, amount in rezept['materials'].items():
            if drachi.inventory.get(material, 0) < amount:
                return await interaction.response.send_message(f"Dir fehlt {amount - drachi.inventory.get(material, 0)}x {material}.", ephemeral=True)
        # Check skill
        for skill, level in rezept['skill_req'].items():
            if drachi.skills.get(skill, 0) < level:
                return await interaction.response.send_message(f"Dein {skill}-Skill ist zu niedrig (braucht {level}).", ephemeral=True)

        # Consume materials & grant item
        for material, amount in rezept['materials'].items():
            drachi.inventory[material] -= amount
        drachi.inventory[gegenstand] = drachi.inventory.get(gegenstand, 0) + 1
        drachi.gain_exp(25)
        self.manager.save()
        await interaction.response.send_message(f"Du hast erfolgreich 1x {gegenstand} hergestellt!")

    # --- Economy Commands ---
    @gotchi.command(name="job", description="Nimm einen Job an oder kündige ihn.")
    async def job(self, interaction: discord.Interaction, aktion: str, job_name: str = None):
        drachi = self.manager.get_drachigotchi(interaction.user.id)
        if not drachi: return await interaction.response.send_message("Starte erst mit `/gotchi start`.", ephemeral=True)

        if aktion == 'annehmen':
            if not job_name or job_name not in JOBS:
                return await interaction.response.send_message("Diesen Job gibt es nicht.", ephemeral=True)
            drachi.job = job_name
            self.manager.save()
            await interaction.response.send_message(f"Du arbeitest jetzt als {JOBS[job_name]['name']}.")
        elif aktion == 'kündigen':
            drachi.job = None
            self.manager.save()
            await interaction.response.send_message("Du bist jetzt arbeitslos.")
        else:
            await interaction.response.send_message("Ungültige Aktion. Wähle 'annehmen' oder 'kündigen'.", ephemeral=True)

    @gotchi.command(name="arbeiten", description="Gehe für 2 Stunden arbeiten.")
    async def arbeiten(self, interaction: discord.Interaction):
        drachi = self.manager.get_drachigotchi(interaction.user.id)
        if not drachi or not drachi.job: 
            return await interaction.response.send_message("Du hast keinen Job.", ephemeral=True)
        
        job_data = JOBS[drachi.job]
        skill_level = drachi.skills.get(job_data['skill'], 1)
        earnings = random.randint(*job_data['income']) + skill_level * 2
        
        drachi.money += earnings
        drachi.hunger = max(0, drachi.hunger - 10)
        drachi.happiness = max(0, drachi.happiness - 5)
        drachi.gain_exp(20)
        self.manager.save()
        await interaction.response.send_message(f"Du hast 2 Stunden gearbeitet und {earnings}€ verdient.")

    # --- Background Task ---
    @tasks.loop(minutes=LOOP_TIME_MINUTES)
    async def update_loop(self):
        now = datetime.now()
        for user_id, drachi in list(self.manager.drachigotchis.items()):
            if drachi.health <= 0:
                if drachi.status != 'dead':
                    drachi.status = 'dead'
                    user = self.bot.get_user(user_id)
                    if user: await user.send(f"Dein Drachigotchi {drachi.name} ist gestorben... Game Over.")
                continue

            # Stats decay
            drachi.hunger = max(0, drachi.hunger - 5)
            drachi.happiness = max(0, drachi.happiness - 4)
            if drachi.hunger == 0: drachi.health = max(0, drachi.health - 5)

            # Random Events
            if random.random() < 0.3: # 30% chance
                event_type = random.choice(['haider', 'donation', 'paket'])
                user = self.bot.get_user(user_id)
                if not user: continue

                if event_type == 'haider':
                    damage = max(1, random.randint(10, 25) - drachi.get_total_stat('defense'))
                    drachi.health = max(0, drachi.health - damage)
                    await user.send(f"🚨 **Haider-Angriff!** 🚨 {drachi.name} hat {damage} HP verloren.")
                elif event_type == 'donation':
                    money_gain = random.randint(20, 100)
                    drachi.money += money_gain
                    await user.send(f"💰 **Spende!** 💰 Ein Brudi hat dir {money_gain}€ dagelassen!")
                elif event_type == 'paket':
                    item_found = random.choice(['mettbrötchen', 'energy_drink', 'holz'])
                    drachi.inventory[item_found] = drachi.inventory.get(item_found, 0) + 1
                    await user.send(f"📦 **Paket!** 📦 Du hast 1x {item_found} erhalten!")

            # Status Update & Aging
            drachi.age = (now - drachi.created_at).days
            drachi.last_update = now

        self.manager.save()

    @update_loop.before_loop
    async def before_update_loop(self):
        await self.bot.wait_until_ready()

async def setup(bot):
    await bot.add_cog(DrachigotchiCog(bot))